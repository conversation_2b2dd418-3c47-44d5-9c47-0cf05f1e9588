#!/usr/bin/env python3

"""
数据加载器抽象基类模块。

本模块定义了数据加载器的抽象接口，所有具体的数据库加载器都必须继承并实现该接口。
支持批量数据加载，并提供了表结构准备和冲突处理机制。

主要功能：
- 定义标准的数据加载器接口
- 支持基于 schema 的目标表准备
- 实现批量数据加载机制
"""

import asyncio
import logging
from abc import ABC, abstractmethod

import pandas as pd
import pyarrow as pa

from ..models import ImportConfig

logger = logging.getLogger(__name__)


class Loader(ABC):
    """数据加载器接口定义。"""

    async def prepare_target(self, schema: pa.Schema, table_name: str) -> None:
        """
        (可选) 在加载数据前准备目标。

        例如，根据 Parquet 文件的 schema 自动创建目标表。
        如果目标表已存在，则此方法可以不执行任何操作。

        :param schema: 源数据的 pyarrow Schema。
        :param table_name: 目标表名。
        """
        logger.info(
            f"prepare_target called for table '{table_name}' with schema containing "
            f"{len(schema.names)} columns: {', '.join(schema.names)}. "
            "Automatic table creation not implemented yet."
        )
        # 保持异步接口以便子类可以实现异步操作
        await asyncio.sleep(0)

    @abstractmethod
    async def load_chunk(self, data: pd.DataFrame, import_config: ImportConfig) -> None:
        """
        将单个数据块加载到目标数据库。

        :param data: 要加载的 pandas DataFrame 数据块。
        :param import_config: 导入配置。
        """
        raise NotImplementedError
