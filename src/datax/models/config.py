#!/usr/bin/env python3

"""
配置模型模块。

本模块定义了数据迁移工具的所有配置模型，使用 Pydantic 进行数据验证和序列化。
包含数据库连接配置、查询配置、导入导出配置等核心数据结构。

主要配置类：
- ConnectionConfig: 数据库连接基础配置
- MySQLConfig/PostgresConfig/ClickHouseConfig/MongoConfig: 特定数据库配置
- QueryConfig: 查询配置
- ExportConfig: 导出配置
- ImportConfig: 导入配置
- S3Config: S3 存储配置
- TranslatorConfig: 数据转换器配置
"""

from datetime import datetime
from enum import Enum
from typing import Any

from pydantic import BaseModel, Field, SecretStr


# 定义数据源类型
class DatasourceKind(Enum):
    """数据源类型常量"""

    MYSQL = "mysql"
    POSTGRES = "postgres"
    CLICKHOUSE = "clickhouse"
    MONGODB = "mongodb"


# 定义冲突处理策略
class ConflictStrategy(Enum):
    """处理数据冲突的策略"""

    IGNORE = "ignore"  # insert ignore
    REPLACE = "replace"  # replace into, delete first then insert
    UPSERT = "upsert"  # insert or update


class ConnectionConfig(BaseModel):
    """数据库连接基本配置。"""

    kind: DatasourceKind = Field(..., description="The kind of the database.")
    host: str = Field(..., description="The host of the database.")
    port: int = Field(..., description="The port of the database.")
    username: str = Field(..., description="The username of the database.")
    # 使用 SecretStr 避免密码在日志中意外泄露
    password: SecretStr | None = Field(..., description="The password of the database.")
    database: str = Field(..., description="The database name.")
    # 使用 SecretStr 避免密码在日志中意外泄露
    connection_string: SecretStr | None = Field(
        default=None, description="The connection string for the database."
    )
    # 在连接建立后立即执行的初始化 SQL
    init_sql: str | None = Field(
        default=None,
        description="SQL statement to execute after establishing the connection.",
    )

    def get_password_value(self) -> str:
        """
        获取密码的实际值。

        返回:
            str: 密码字符串，如果没有密码则返回空字符串
        """
        return self.password.get_secret_value() if self.password else ""


class MySQLConfig(ConnectionConfig):
    """MySQL 连接配置"""

    kind: DatasourceKind = DatasourceKind.MYSQL
    port: int = 3306


class PostgresConfig(ConnectionConfig):
    """PostgreSQL 连接配置"""

    kind: DatasourceKind = DatasourceKind.POSTGRES
    port: int = 5432
    schema_name: str | None = None


class ClickHouseConfig(ConnectionConfig):
    """ClickHouse 连接配置"""

    kind: DatasourceKind = DatasourceKind.CLICKHOUSE
    port: int = 8123
    username: str = Field(default="default", description="ClickHouse username")
    database: str = Field(default="default", description="ClickHouse database")


class MongoConfig(ConnectionConfig):
    """MongoDB 连接配置"""

    kind: DatasourceKind = DatasourceKind.MONGODB
    port: int = 27017


AnyDatabaseConfig = MySQLConfig | PostgresConfig | ClickHouseConfig | MongoConfig


class QueryConfig(BaseModel):
    """定义如何从源数据库查询数据的配置。"""

    table_name: str = Field(..., description="The table to query from.")
    # 允许用户提供完整的自定义 SQL 查询
    sql_query: str | None = Field(
        default=None, description="A full custom SQL query to execute."
    )
    # 如果不提供 sql_query，则可以指定要导出的列
    columns: list[str] | None = Field(
        default=None, description="List of columns to export."
    )
    # SQL WHERE 条件，用于过滤数据
    sql_condition: str | None = Field(
        default=None, description="SQL WHERE condition for filtering data."
    )
    # 限制导出的总行数
    limit: int | None = Field(
        default=None, description="Total number of rows to limit."
    )
    # 按时间范围过滤
    time_range: tuple[datetime, datetime] | None = Field(
        default=None, description="Time range for filtering."
    )


class TranslatorConfig(BaseModel):
    """转换器配置，定义了要应用的数据转换类型和参数。"""

    name: str = Field(..., description="The name of the translator.")
    kind: DatasourceKind = Field(
        description="The type of translator to use, e.g., 'column_rename'."
    )
    params: dict[str, Any] = Field(
        default_factory=dict, description="Parameters specific to the translator."
    )


class S3Config(BaseModel):
    """S3 存储配置。"""

    bucket: str = Field(..., description="The bucket name for S3 storage.")
    key: str = Field(default="", description="The S3 key prefix for object storage.")
    access_key: str = Field(..., description="The access key for S3 access.")
    secret_key: SecretStr = Field(..., description="The secret key for S3 access.")
    region: str = Field(default="us-east-1", description="The AWS region for S3.")
    endpoint_url: str | None = Field(
        default=None, description="Custom S3 endpoint URL (for MinIO, etc.)."
    )
    # S3 传输的最大并发线程数
    max_concurrency: int = Field(
        default=10, description="Max concurrency for S3 transfers."
    )
    # S3 传输的最大带宽（MB/s）
    max_bandwidth_mbps: int = Field(
        default=10, description="Max bandwidth in MB/s for S3 transfers."
    )


class ExportConfig(BaseModel):
    """完整的导出作业配置。"""

    source_db: AnyDatabaseConfig = Field(
        ..., description="The source database configuration to export data from."
    )
    query: QueryConfig = Field(
        ..., description="The query to execute on the source database."
    )
    s3_target: S3Config = Field(..., description="The S3 bucket to export data to.")
    translators: list[TranslatorConfig] | None = Field(
        default=None, description="Optional data translator configuration."
    )
    # 每次从数据库游标中获取的行数
    cursor_fetch_size: int = Field(
        default=1000, description="Number of rows to fetch from DB cursor at a time."
    )
    # 累积到内存中并写入单个 Parquet 批次的行数
    parquet_chunk_size: int = Field(
        default=10_000, description="Number of rows to accumulate for a Parquet chunk."
    )
    # 每个 Parquet 文件的总行数（用于分片）
    rows_per_file: int | None = Field(
        default=1_000_000, description="Number of rows per output Parquet file."
    )


class ImportConfig(BaseModel):
    """
    完整的导入作业配置。

    注意：conflict_target字段已被移除，系统现在会自动从数据库元数据中
    获取主键和唯一约束信息来处理数据冲突。
    """

    table_name: str
    s3_source: S3Config
    target_db: AnyDatabaseConfig = Field(
        ..., description="The target database configuration to import data into."
    )
    translators: list[TranslatorConfig] | None = Field(
        default=None, description="Optional data translator configuration."
    )
    conflict_strategy: ConflictStrategy = Field(
        default=ConflictStrategy.IGNORE,
        description="Strategy to handle primary key conflicts. The system will automatically discover conflict targets from table metadata.",
    )
