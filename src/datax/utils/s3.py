#!/usr/bin/env python3

"""
S3 工具模块。

本模块提供了与 Amazon S3 交互的异步工具函数和类。
支持文件上传、下载、列表、删除等操作，并提供了批量操作和生命周期策略管理功能。

主要功能：
- AsyncS3Client: 异步 S3 客户端类，提供上下文管理器支持和完整的 S3 操作功能
"""

import asyncio
import logging
from collections.abc import AsyncGenerator
from types import TracebackType
from typing import Self

import aioboto3  # type: ignore
import aiofiles  # type: ignore
import yaml
from boto3.s3.transfer import TransferConfig
from botocore.client import BaseClient
from botocore.exceptions import ClientError

from ..models import S3Config

logger = logging.getLogger(__name__)


class AsyncS3Client:
    """
    异步S3客户端，提供上下文管理器支持。
    """

    _CLIENT_NOT_INITIALIZED_ERROR = "Client not initialized. Use async context manager."

    def __init__(self, s3_config: S3Config):
        self.s3_config = s3_config
        self.session = aioboto3.Session()
        self.client: BaseClient | None = None

    async def __aenter__(self) -> Self:
        # 构建 S3 客户端配置
        client_config = {
            "aws_access_key_id": self.s3_config.access_key,
            "aws_secret_access_key": self.s3_config.secret_key.get_secret_value(),
            "region_name": self.s3_config.region,
            "use_ssl": self.s3_config.endpoint_url is None,
        }

        # 如果指定了自定义端点（如 MinIO），则添加端点配置
        if self.s3_config.endpoint_url:
            client_config["endpoint_url"] = self.s3_config.endpoint_url

        self.client = await self.session.client("s3", **client_config).__aenter__()
        return self

    async def __aexit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: TracebackType | None,
    ) -> None:
        if self.client:
            await self.client.__aexit__(exc_type, exc_val, exc_tb)

    async def upload_file(self, local_path: str, s3_key: str) -> None:
        """上传文件到S3"""
        if self.client is None:
            raise RuntimeError(self._CLIENT_NOT_INITIALIZED_ERROR)

        logger.info(
            f"Uploading {local_path} to s3://{self.s3_config.bucket}/{s3_key} "
            f"with max_concurrency={self.s3_config.max_concurrency} "
            f"and max_bandwidth={self.s3_config.max_bandwidth_mbps} MB/s."
        )

        try:
            config = TransferConfig(
                max_concurrency=self.s3_config.max_concurrency,
                max_bandwidth=self.s3_config.max_bandwidth_mbps * 1024 * 1024,
            )
            await self.client.upload_file(
                local_path,
                self.s3_config.bucket,
                s3_key,
                Config=config,
            )
            logger.info(f"Successfully uploaded {s3_key}.")
        except ClientError as e:
            logger.error(f"Failed to upload {local_path} to S3: {e}")
            raise

    async def download_file(self, s3_key: str, local_path: str) -> None:
        """从S3下载文件"""
        if self.client is None:
            raise RuntimeError(self._CLIENT_NOT_INITIALIZED_ERROR)

        logger.info(
            f"Downloading s3://{self.s3_config.bucket}/{s3_key} to {local_path}"
        )

        try:
            config = TransferConfig(
                max_concurrency=self.s3_config.max_concurrency,
                max_bandwidth=self.s3_config.max_bandwidth_mbps * 1024 * 1024,
            )
            await self.client.download_file(
                self.s3_config.bucket,
                s3_key,
                local_path,
                Config=config,
            )
            logger.info(f"Successfully downloaded {s3_key}.")
        except ClientError as e:
            logger.error(f"Failed to download {s3_key} from S3: {e}")
            raise

    async def delete_object(self, s3_key: str) -> None:
        """删除S3对象"""
        if self.client is None:
            raise RuntimeError(self._CLIENT_NOT_INITIALIZED_ERROR)

        logger.info(f"Deleting s3://{self.s3_config.bucket}/{s3_key}")

        try:
            await self.client.delete_object(Bucket=self.s3_config.bucket, Key=s3_key)
            logger.info(f"Successfully deleted {s3_key}.")
        except ClientError as e:
            logger.error(f"Failed to delete {s3_key} from S3: {e}")
            raise

    async def list_objects(self, prefix: str = "") -> AsyncGenerator[str]:
        """列出S3对象"""
        if self.client is None:
            raise RuntimeError(self._CLIENT_NOT_INITIALIZED_ERROR)

        # 使用 list_objects_v2 直接调用，然后手动处理分页
        continuation_token = None

        while True:
            # 构建请求参数
            params = {
                "Bucket": self.s3_config.bucket,
                "Prefix": prefix,
            }

            if continuation_token:
                params["ContinuationToken"] = continuation_token

            # 调用 list_objects_v2
            response = await self.client.list_objects_v2(**params)

            # 处理当前页的对象
            if "Contents" in response:
                for obj in response["Contents"]:
                    yield obj["Key"]

            # 检查是否还有更多页面
            if not response.get("IsTruncated", False):
                break

            continuation_token = response.get("NextContinuationToken")

    async def upload_multiple_files(
        self,
        file_mappings: list[tuple[str, str]],
        max_concurrent_uploads: int = 5,
    ) -> None:
        """
        异步批量上传多个文件到S3，支持并发控制。

        Args:
            file_mappings: 文件映射列表，每个元素为(本地路径, S3键名)的元组
            max_concurrent_uploads: 最大并发上传数
        """
        if self.client is None:
            raise RuntimeError(self._CLIENT_NOT_INITIALIZED_ERROR)

        semaphore = asyncio.Semaphore(max_concurrent_uploads)

        async def upload_with_semaphore(local_path: str, s3_key: str) -> None:
            async with semaphore:
                await self.upload_file(local_path, s3_key)

        tasks = [
            upload_with_semaphore(local_path, s3_key)
            for local_path, s3_key in file_mappings
        ]

        logger.info(
            f"Starting batch upload of {len(tasks)} files with max concurrency {max_concurrent_uploads}"
        )
        await asyncio.gather(*tasks, return_exceptions=True)
        logger.info("Batch upload completed")

    async def apply_lifecycle_policy(self, config_path: str) -> None:
        """
        异步加载YAML文件中的生命周期规则并应用到S3桶。
        这将覆盖桶上的任何现有生命周期配置。

        Args:
            config_path: 生命周期配置文件路径
        """
        if self.client is None:
            raise RuntimeError(self._CLIENT_NOT_INITIALIZED_ERROR)

        try:
            async with aiofiles.open(config_path, "rb") as f:
                content = await f.read()
                config = yaml.safe_load(content)
        except FileNotFoundError:
            logger.error(f"Lifecycle config file not found at: {config_path}")
            raise
        except yaml.YAMLError as e:
            logger.error(f"Error parsing YAML file {config_path}: {e}")
            raise

        rules = []
        for rule_config in config.get("rules", []):
            rules.append(
                {
                    "ID": rule_config["id"],
                    "Filter": {"Prefix": rule_config["prefix"]},
                    "Status": "Enabled",
                    "Expiration": {"Days": rule_config["expiration_days"]},
                }
            )

        if not rules:
            logger.warning("No rules found in the lifecycle config file.")
            return

        lifecycle_configuration = {"Rules": rules}

        try:
            await self.client.put_bucket_lifecycle_configuration(
                Bucket=self.s3_config.bucket,
                LifecycleConfiguration=lifecycle_configuration,
            )
            logger.info(
                f"Successfully applied lifecycle configuration from {config_path} to bucket '{self.s3_config.bucket}'."
            )
        except ClientError as e:
            logger.error(
                f"Failed to apply lifecycle policy to bucket '{self.s3_config.bucket}': {e}"
            )
            raise
