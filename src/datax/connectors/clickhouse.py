#!/usr/bin/env python3

"""
ClickHouse 连接器模块。

本模块提供了 ClickHouse 数据库的提取器和加载器实现。
支持异步操作，使用 clickhouse-connect 库进行高性能数据处理。
提供了完整的连接管理、查询构建和批量数据加载功能。

主要功能：
- ClickHouseConnector: 继承自 Connector 基类的连接管理类
- ClickHouseExtractor: 流式数据提取器，支持分批读取
- ClickHouseLoader: 批量数据加载器，支持高性能批量插入
- 支持自定义初始化 SQL 命令
- 支持数据压缩传输
"""

import logging
from collections.abc import AsyncIterator
from types import TracebackType
from typing import TYPE_CHECKING, Any, Self

import pandas as pd
import pyarrow as pa
from clickhouse_connect import get_async_client
from clickhouse_connect.driver.exceptions import ClickHouseError

from ..core.extractor import Extractor
from ..core.loader import Loader
from ..models import (
    ClickHouseConfig,
    ColumnInfo,
    ColumnType,
    ConstraintInfo,
    DatasourceKind,
    IndexInfo,
    IndexType,
    QueryConfig,
    TableMetadata,
)
from .query_builder import QueryBuilder

if TYPE_CHECKING:
    from clickhouse_connect.driver.asyncclient import AsyncClient

logger = logging.getLogger(__name__)


class ClickHouseConnector:
    """ClickHouse 连接器。

    提供数据库连接管理功能，包括连接建立、配置和关闭。
    支持异步上下文管理器，确保连接的正确建立和释放。
    """

    def __init__(self, config: ClickHouseConfig):
        """
        初始化 ClickHouse 连接器。

        参数:
            config: ClickHouse 配置对象，包含连接参数
        """
        self.config = config
        self.connection: AsyncClient | None = None
        logger.debug("ClickHouseConnector initialized with config")

    async def _open(self) -> None:
        """
        建立并配置 ClickHouse 连接。

        使用配置中的参数创建新的 ClickHouse 客户端连接。
        连接建立后，如果提供了 init_sql，则执行初始化 SQL。

        异常:
            ClickHouseError: 连接失败或 init_sql 执行失败时抛出
        """
        if self.connection:
            # Connection already exists
            logger.debug("ClickHouse connection already established")
            return

        try:
            # Get password value if it exists
            password = None
            if self.config.password:
                password = self.config.get_password_value()

            # Create ClickHouse async client with compression enabled
            self.connection = await get_async_client(
                host=self.config.host,
                port=self.config.port,
                username=self.config.username,
                password=password or "",
                database=self.config.database,
                compress=True,
            )

            logger.info(
                f"ClickHouse connection established to {self.config.host}:{self.config.port}/{self.config.database}"
            )

            # Execute optional initialization SQL
            if self.config.init_sql:
                logger.info(f"Executing init_sql: {self.config.init_sql}")
                await self.connection.command(self.config.init_sql)
                logger.debug("init_sql executed successfully")

        except Exception as e:
            logger.error(f"Unexpected error during ClickHouse connection: {e}")
            self.connection = None
            raise

    async def close(self) -> None:
        """关闭连接。

        设置 self.connection 为 None 并关闭连接。
        """
        if self.connection:
            try:
                conn = self.connection  # type: AsyncClient
                await conn.close()
                logger.debug("ClickHouse connection disconnected")
            except Exception as e:
                logger.error(f"Error during ClickHouse disconnect: {e}")
            finally:
                # 始终将连接设置为 None
                self.connection = None
                logger.debug("ClickHouse connection closed")

    async def __aenter__(self) -> Self:
        """异步上下文管理器入口。"""
        await self._open()
        return self

    async def __aexit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: TracebackType | None,
    ) -> None:
        """异步上下文管理器出口。"""
        await self.close()

    async def query(self, query: str, parameters: dict | None = None):
        """执行查询并返回结果。

        参数:
            query: SQL查询语句
            parameters: 查询参数字典

        返回:
            QueryResult: 查询结果对象

        异常:
            ConnectionError: 连接未建立
        """
        if not self.connection:
            raise ConnectionError("ClickHouse connection not established")

        return await self.connection.query(query, parameters)

    async def command(self, cmd: str, parameters: dict | None = None):
        """执行命令并返回状态信息。

        参数:
            cmd: SQL命令
            parameters: 命令参数字典

        返回:
            执行状态信息

        异常:
            ConnectionError: 连接未建立
        """
        if not self.connection:
            raise ConnectionError("ClickHouse connection not established")

        return await self.connection.command(cmd, parameters)

    async def insert(self, table: str, data, column_names: list[str] | None = None):
        """插入数据到表中。

        参数:
            table: 表名
            data: 要插入的数据
            column_names: 列名列表

        异常:
            ConnectionError: 连接未建立
        """
        if not self.connection:
            raise ConnectionError("ClickHouse connection not established")

        return await self.connection.insert(table, data, column_names or [])


class ClickHouseExtractor(Extractor):
    """使用流式迭代器的 ClickHouse 数据提取器。

    实现了ClickHouse特定的数据提取逻辑。
    使用clickhouse-connect库进行流式数据处理，适合处理大规模数据集。
    支持灵活的查询构建，包括直接SQL查询、条件过滤、列选择等。
    """

    def __init__(self, config: ClickHouseConfig):
        """
        初始化 ClickHouse 提取器。

        参数:
            config: ClickHouse 配置对象
        """
        self._connector = ClickHouseConnector(config)
        self.query_builder = QueryBuilder(DatasourceKind.CLICKHOUSE)

    def _build_query(self, query_config: QueryConfig) -> str:
        """
        根据 QueryConfig 模型构建 SQL 查询语句。

        使用通用查询构造器，支持四种优先级的查询构造：
        1. 直接SQL查询优先级最高
        2. 条件查询构造（sql_condition参数）
        3. 结果集限制（limit参数）
        4. 列选择（columns参数）

        参数:
            query_config: 查询配置对象

        返回:
            str: 构建的 SQL 查询语句
        """
        return self.query_builder.build_sql_query(query_config)

    async def extract_stream(
        self, query_config: QueryConfig, export_config: Any
    ) -> AsyncIterator[pd.DataFrame]:
        """
        使用 ClickHouse query_df_stream 进行流式数据提取。

        参数:
            query_config: 查询配置对象
            export_config: 导出配置对象

        Yields:
            pd.DataFrame: 数据块
        """
        async with self._connector:
            query = self._build_query(query_config)
            logger.info(f"Executing ClickHouse query: {query}")

            try:
                # Use query_df_stream for streaming DataFrame chunks
                conn = self._connector.connection  # type: AsyncClient  # type: ignore
                stream_context = await conn.query_df_stream(
                    query,
                    settings={"max_block_size": export_config.cursor_fetch_size},
                )

                with stream_context as stream:
                    for df_chunk in stream:
                        if not df_chunk.empty:
                            logger.debug(
                                f"Yielding ClickHouse chunk with {len(df_chunk)} rows"
                            )
                            yield df_chunk

            except Exception:
                # 错误传播，由上层处理
                raise


class ClickHouseLoader(Loader):
    """ClickHouse 数据加载器。

    实现了ClickHouse特定的数据加载逻辑。
    支持批量数据加载和高性能批量插入。
    自动处理数据压缩传输，确保高效的数据传输。
    """

    def __init__(self, config: ClickHouseConfig):
        """
        初始化 ClickHouse 加载器。

        参数:
            config: ClickHouse 配置对象
        """
        self._connector = ClickHouseConnector(config)

    async def prepare_target(self, schema: pa.Schema, table_name: str) -> None:
        """
        准备目标表（如需要）。

        注意：对于 ClickHouse 这是一个空操作，因为表创建通常在外部处理，
        以便指定特定的引擎类型。

        参数:
            schema: 数据的 pyarrow Schema
            table_name: 目标表名
        """
        # Call parent implementation for logging
        await super().prepare_target(schema, table_name)
        logger.info(
            f"ClickHouse prepare_target: Table creation skipped for '{table_name}'. "
            "Tables should be pre-created with appropriate engine types (e.g., MergeTree, ReplacingMergeTree)."
        )

    async def load_chunk(self, data: pd.DataFrame, import_config: Any) -> None:
        """
        将 DataFrame 数据块加载到 ClickHouse。

        参数:
            data: 要加载的 DataFrame
            import_config: 导入配置对象
        """
        # Early return on empty DataFrame
        if data.empty:
            logger.debug("Empty DataFrame provided, skipping load")
            return

        async with self._connector:
            table_name = import_config.table_name
            conflict_strategy = import_config.conflict_strategy

            try:
                conn = self._connector.connection  # type: AsyncClient  # type: ignore
                if conflict_strategy == "replace":
                    # For replace strategy, we need to handle table engine-specific logic
                    # This implementation assumes the table might be ReplacingMergeTree
                    # or another engine that supports some form of replacement
                    logger.warning(
                        f"Replace strategy for ClickHouse table '{table_name}' assumes "
                        "ReplacingMergeTree engine or similar. For other engines, this "
                        "will perform a simple insert. Consider using ALTER TABLE DELETE "
                        "or recreating the table if full replacement is needed."
                    )

                    # Use insert_df with settings for replace behavior
                    await conn.insert_df(
                        table_name, data, settings={"replace_running": 1}
                    )

                elif conflict_strategy == "ignore":
                    # For ignore strategy, perform plain insert
                    # Deduplication should be handled by MergeTree logic or caller
                    await conn.insert_df(table_name, data)

                logger.info(
                    f"Loaded {len(data)} rows into ClickHouse table '{table_name}' using '{conflict_strategy}' strategy"
                )

            except ClickHouseError as e:
                logger.error(f"Error loading data into ClickHouse: {e}")
                raise


class ClickHouseMetadataProvider:
    """ClickHouse 元数据查询提供者。

    实现了ClickHouse特定的元数据查询逻辑。
    通过查询system表获取表结构、索引、约束等元数据信息。
    支持完整的表元数据提取，包括列信息、索引信息和约束信息。
    """

    def __init__(self, config: ClickHouseConfig):
        """
        初始化 ClickHouse 元数据查询提供者。

        参数:
            config: ClickHouse 配置对象
        """
        self._connector = ClickHouseConnector(config)

    def _map_clickhouse_type_to_column_type(self, ch_type: str) -> ColumnType:
        """将 ClickHouse 数据类型映射到标准列类型"""
        ch_type = ch_type.lower()

        # 数值类型
        if ch_type in ("int8", "tinyint"):
            return ColumnType.TINYINT
        elif ch_type in ("int16", "smallint"):
            return ColumnType.SMALLINT
        elif ch_type in ("int32", "int", "integer"):
            return ColumnType.INTEGER
        elif ch_type in ("int64", "bigint"):
            return ColumnType.BIGINT
        elif ch_type.startswith("decimal") or ch_type.startswith("numeric"):
            return ColumnType.DECIMAL
        elif ch_type in ("float32", "float"):
            return ColumnType.FLOAT
        elif ch_type in ("float64", "double"):
            return ColumnType.DOUBLE

        # 字符串类型
        elif ch_type.startswith("string") or ch_type.startswith("fixedstring"):
            return ColumnType.VARCHAR

        # 日期时间类型
        elif ch_type == "date":
            return ColumnType.DATE
        elif ch_type == "datetime" or ch_type.startswith("datetime64"):
            return ColumnType.DATETIME

        # 布尔类型
        elif ch_type in ("bool", "boolean"):
            return ColumnType.BOOLEAN

        # UUID类型
        elif ch_type == "uuid":
            return ColumnType.UUID

        # 枚举类型
        elif ch_type.startswith("enum"):
            return ColumnType.ENUM

        return ColumnType.UNKNOWN

    async def get_table_metadata(
        self, table_name: str, schema_name: str | None = None
    ) -> TableMetadata:
        """获取 ClickHouse 表的元数据信息"""
        async with self._connector:
            # 检查表是否存在
            if not await self.table_exists(table_name, schema_name):
                raise ValueError(
                    f"Table '{table_name}' does not exist in database '{self._connector.config.database}'"
                )

            # 查询列信息
            columns = await self._get_column_info(table_name)

            # 查询索引信息（ClickHouse中主要是排序键和主键）
            indexes = await self._get_index_info(table_name)

            # 查询约束信息（ClickHouse约束较少）
            constraints = await self._get_constraint_info(table_name)

            # 查询表注释
            table_comment = await self._get_table_comment(table_name)

            return TableMetadata(
                table_name=table_name,
                database_name=self._connector.config.database,
                columns=columns,
                indexes=indexes,
                constraints=constraints,
                comment=table_comment,
            )

    async def _get_column_info(self, table_name: str) -> list[ColumnInfo]:
        """查询表的列信息"""
        query = f"""
            SELECT
                name,
                type,
                default_kind,
                default_expression,
                comment
            FROM system.columns
            WHERE database = '{self._connector.config.database}' AND table = '{table_name}'
            ORDER BY position
        """

        conn = self._connector.connection  # type: AsyncClient  # type: ignore
        result = await conn.query(query)

        columns = []
        for row in result.result_rows:
            name, type_str, default_kind, default_expr, comment = row

            # 检查是否为主键（通过排序键判断）
            is_primary_key = await self._is_column_in_primary_key(table_name, name)

            columns.append(
                ColumnInfo(
                    name=name,
                    data_type=self._map_clickhouse_type_to_column_type(type_str),
                    is_nullable=type_str.startswith("Nullable("),
                    is_primary_key=is_primary_key,
                    is_unique=is_primary_key,  # ClickHouse中主键即唯一
                    is_auto_increment=False,  # ClickHouse没有自增
                    default_value=default_expr if default_kind else None,
                    comment=comment,
                )
            )

        return columns

    async def _is_column_in_primary_key(
        self, table_name: str, column_name: str
    ) -> bool:
        """检查列是否在主键中"""
        query = f"""
            SELECT primary_key
            FROM system.tables
            WHERE database = '{self._connector.config.database}' AND name = '{table_name}'
        """

        conn = self._connector.connection  # type: AsyncClient  # type: ignore
        result = await conn.query(query)
        if result.result_rows:
            primary_key = result.result_rows[0][0]
            if primary_key:
                # 简单检查列名是否在主键定义中
                return column_name in primary_key

        return False

    async def _get_index_info(self, table_name: str) -> list[IndexInfo]:
        """查询表的索引信息（主要是排序键）"""
        indexes = []

        # 查询表的排序键和主键
        query = f"""
            SELECT
                sorting_key,
                primary_key
            FROM system.tables
            WHERE database = '{self._connector.config.database}' AND name = '{table_name}'
        """

        conn = self._connector.connection  # type: AsyncClient  # type: ignore
        result = await conn.query(query)
        if result.result_rows:
            sorting_key, primary_key = result.result_rows[0]

            # 添加主键索引
            if primary_key:
                pk_columns = self._parse_key_columns(primary_key)
                indexes.append(
                    IndexInfo(
                        name="PRIMARY",
                        index_type=IndexType.PRIMARY,
                        columns=pk_columns,
                        is_unique=True,
                        is_primary=True,
                    )
                )

            # 添加排序键索引（如果与主键不同）
            if sorting_key and sorting_key != primary_key:
                sort_columns = self._parse_key_columns(sorting_key)
                indexes.append(
                    IndexInfo(
                        name="SORTING_KEY",
                        index_type=IndexType.INDEX,
                        columns=sort_columns,
                        is_unique=False,
                        is_primary=False,
                    )
                )

        return indexes

    def _parse_key_columns(self, key_definition: str) -> list[str]:
        """解析键定义中的列名"""
        if not key_definition:
            return []

        # 简单解析，去除括号和空格，按逗号分割
        key_definition = key_definition.strip()
        if key_definition.startswith("(") and key_definition.endswith(")"):
            key_definition = key_definition[1:-1]

        columns = [col.strip() for col in key_definition.split(",")]
        return [col for col in columns if col]

    async def _get_constraint_info(self, table_name: str) -> list[ConstraintInfo]:
        """查询表的约束信息（ClickHouse约束较少）"""
        constraints = []

        # ClickHouse主要约束是主键，已在索引中处理
        # 未来可以扩展其他约束类型

        return constraints

    async def _get_table_comment(self, table_name: str) -> str | None:
        """查询表注释"""
        query = f"""
            SELECT comment
            FROM system.tables
            WHERE database = '{self._connector.config.database}' AND name = '{table_name}'
        """

        conn = self._connector.connection  # type: AsyncClient  # type: ignore
        result = await conn.query(query)
        if result.result_rows:
            comment = result.result_rows[0][0]
            return comment if comment else None

        return None

    async def table_exists(
        self, table_name: str, schema_name: str | None = None
    ) -> bool:
        """检查表是否存在"""
        # ClickHouse没有schema概念，忽略schema_name
        _ = schema_name

        # 直接使用已存在的连接，避免嵌套异步上下文管理器
        query = f"""
            SELECT COUNT(*)
            FROM system.tables
            WHERE database = '{self._connector.config.database}' AND name = '{table_name}'
        """

        conn = self._connector.connection  # type: AsyncClient  # type: ignore
        result = await conn.query(query)
        return result.result_rows[0][0] > 0 if result.result_rows else False

    async def get_database_name(self) -> str:
        """获取当前连接的数据库名称"""
        return self._connector.config.database
