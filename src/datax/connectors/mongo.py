#!/usr/bin/env python3

"""
MongoDB 连接器模块。

本模块提供了 MongoDB 数据库的提取器和加载器实现。
支持异步操作，使用 motor 库进行高性能数据处理。
提供了完整的连接管理、查询构建和批量数据加载功能。

主要功能：
- MongoConnector: 继承自 Connector 基类的连接管理类
- MongoExtractor: 流式数据提取器，支持游标分批读取
- MongoLoader: 批量数据加载器，支持 upsert 操作
- 支持自定义查询和投影
- 支持 ObjectId 类型处理
"""

from __future__ import annotations

import logging
from collections.abc import AsyncIterator
from types import TracebackType
from typing import TYPE_CHECKING, Any, Self

import pandas as pd
import pyarrow as pa
from bson import ObjectId

from ..core.extractor import Extractor
from ..core.loader import Loader

# 条件导入motor，兼容Python 3.13
if TYPE_CHECKING:
    from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
else:
    try:
        from motor.motor_asyncio import AsyncIOMotorClient  # type: ignore

        MOTOR_AVAILABLE = True
    except (ImportError, AttributeError) as e:
        # 在Python 3.13中，motor可能因为asyncio.coroutine被移除而失败
        print(
            f"Warning: motor not available: {e}. MongoDB connector will not be available."
        )
        MOTOR_AVAILABLE = False
        AsyncIOMotorClient = None  # type: ignore

from ..core.metadata import CachedMetadataProvider
from ..models import (
    ColumnInfo,
    ColumnType,
    ConflictStrategy,
    ConstraintInfo,
    ExportConfig,
    ImportConfig,
    IndexInfo,
    IndexType,
    MongoConfig,
    QueryConfig,
    TableMetadata,
)
from .query_builder import MongoQueryBuilder

logger = logging.getLogger(__name__)


class MongoConnector:
    """MongoDB 连接器。

    提供数据库连接管理功能，包括连接建立、配置和关闭。
    支持异步上下文管理器，确保连接的正确建立和释放。
    """

    def __init__(self, config: MongoConfig):
        """
        初始化 MongoDB 连接器。

        参数:
            config: MongoDB 配置对象，包含连接参数
        """
        self.config = config
        self.connection: AsyncIOMotorClient | None = None
        self.database: str | None = None
        logger.debug("MongoConnector initialized with config")

    async def _open(self) -> None:
        """
        建立并配置 MongoDB 连接。

        使用配置中的参数创建新的 MongoDB 客户端连接。

        异常:
            PyMongoError: 连接失败时抛出
        """
        if self.connection:
            # Connection already exists
            logger.debug("MongoDB connection already established")
            return

        try:
            # Build connection URI
            auth_part = ""
            if self.config.username and self.config.password:
                password = self.config.get_password_value()
                auth_part = f"{self.config.username}:{password}@"

            # Use connection_string if provided, otherwise build from components
            if self.config.connection_string:
                uri = self.config.connection_string.get_secret_value()
            else:
                uri = f"mongodb://{auth_part}{self.config.host}:{self.config.port}/{self.config.database}"

            # Create MongoDB client
            self.connection = AsyncIOMotorClient(uri, serverSelectionTimeoutMS=5000)

            # Test connection by pinging the server
            await self.connection.admin.command("ping")

            # Get database reference
            self.database = self.connection[self.config.database]

            logger.info(
                f"MongoDB connection established to {self.config.host}:{self.config.port}/{self.config.database}"
            )

            # Execute optional initialization commands
            if self.config.init_sql:
                # For MongoDB, init_sql could be JavaScript code or admin commands
                # This is a simplified implementation - in practice you might want to
                # parse and execute specific MongoDB commands
                logger.warning(
                    f"MongoDB init_sql not fully implemented: {self.config.init_sql}"
                )
                logger.debug("init_sql skipped for MongoDB (not implemented)")

        except Exception as e:
            logger.error(f"Unexpected error during MongoDB connection: {e}")
            self.connection = None
            self.database = None
            raise

    async def close(self) -> None:
        """关闭 MongoDB 客户端连接。"""
        if self.connection:
            try:
                # Type assertion for MongoDB client
                client = self.connection  # type: AsyncIOMotorClient  # type: ignore
                client.close()
                logger.debug("MongoDB connection closed")
            except Exception as e:
                logger.error(f"Error during MongoDB disconnect: {e}")
            finally:
                # 始终将连接设置为 None
                self.connection = None
                self.database = None
                logger.debug("MongoDB connection cleanup completed")

    async def __aenter__(self) -> Self:
        """异步上下文管理器入口。"""
        await self._open()
        return self

    async def __aexit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: TracebackType | None,
    ) -> None:
        """异步上下文管理器出口。"""
        await self.close()

    def get_collection(self, collection_name: str):
        """获取集合对象。

        参数:
            collection_name: 集合名称

        返回:
            Collection: MongoDB集合对象

        异常:
            ConnectionError: 连接未建立
        """
        if self.database is None:
            raise ConnectionError("MongoDB database not established")

        return self.database[collection_name]

    async def list_collection_names(self) -> list[str]:
        """列出数据库中的所有集合名称。

        返回:
            list[str]: 集合名称列表

        异常:
            ConnectionError: 连接未建立
        """
        if self.database is None:
            raise ConnectionError("MongoDB database not established")

        return await self.database.list_collection_names()

    async def create_collection(self, name: str, **kwargs):
        """创建新集合。

        参数:
            name: 集合名称
            **kwargs: 创建集合的其他参数

        返回:
            Collection: 新创建的集合对象

        异常:
            ConnectionError: 连接未建立
        """
        if self.database is None:
            raise ConnectionError("MongoDB database not established")

        return await self.database.create_collection(name, **kwargs)


class MongoExtractor(Extractor):
    """使用游标迭代的 MongoDB 数据提取器。

    实现了MongoDB特定的数据提取逻辑。
    使用motor库进行异步数据处理，适合处理大规模数据集。
    支持灵活的查询构建，包括直接查询、条件过滤、列选择等。
    """

    def __init__(self, config: MongoConfig):
        """
        初始化 MongoDB 提取器。

        参数:
            config: MongoDB 配置对象
        """
        self._connector = MongoConnector(config)
        self.query_builder = MongoQueryBuilder()

    def _build_query(
        self, query_config: QueryConfig
    ) -> tuple[dict[str, Any], dict[str, Any]]:
        """
        根据 QueryConfig 构建 MongoDB 查询和投影。

        使用MongoDB查询构造器，支持：
        1. 直接查询（sql_query作为JSON过滤条件）
        2. 条件查询（sql_condition作为JSON过滤条件）
        3. 列选择（columns参数）
        4. 结果集限制（在extract_stream中处理）

        参数:
            query_config: 包含集合和查询参数的 QueryConfig

        返回:
            tuple: (filter_dict, projection_dict) 用于 MongoDB find 操作
        """
        return self.query_builder.build_mongo_query(query_config)

    async def extract_stream(
        self, query_config: QueryConfig, export_config: ExportConfig
    ) -> AsyncIterator[pd.DataFrame]:
        """
        使用 MongoDB 游标迭代进行流式数据提取。

        参数:
            query_config: 查询配置对象
            export_config: 导出配置对象

        Yields:
            pd.DataFrame: 数据块
        """
        async with self._connector:
            if self._connector.connection is None or self._connector.database is None:
                raise ConnectionError("MongoDB connection not established.")

            # Get collection
            collection = self._connector.database[query_config.table_name]

            # Build query and projection
            filter_dict, projection_dict = self._build_query(query_config)

            logger.info(
                f"Executing MongoDB query on collection '{query_config.table_name}': filter={filter_dict}, projection={projection_dict}"
            )

            buffer: list[dict[str, Any]] = []

            try:
                # Create cursor with batch size
                cursor = collection.find(filter_dict, projection_dict)

                # Apply limit if specified
                if query_config.limit:
                    cursor = cursor.limit(query_config.limit)

                # Set batch size for efficient streaming
                cursor = cursor.batch_size(export_config.cursor_fetch_size)

                async for document in cursor:
                    # Convert ObjectId to string for JSON serialization
                    processed_doc = self._process_document(document)
                    buffer.append(processed_doc)

                    if len(buffer) >= export_config.parquet_chunk_size:
                        logger.debug(f"Yielding MongoDB chunk with {len(buffer)} rows")
                        yield pd.DataFrame(buffer)
                        buffer.clear()

                # Yield any remaining rows in the buffer
                if buffer:
                    logger.debug(
                        f"Yielding final MongoDB chunk with {len(buffer)} rows"
                    )
                    yield pd.DataFrame(buffer)

            except Exception:
                # 错误传播，由上层处理
                raise

    def _process_document(self, document: dict[str, Any]) -> dict[str, Any]:
        """
        处理 MongoDB 文档以兼容 DataFrame。

        将 ObjectId 和其他 BSON 类型转换为可 JSON 序列化的格式。

        参数:
            document: 原始 MongoDB 文档

        返回:
            dict: 处理后的包含可序列化值的文档
        """
        processed = {}

        for key, value in document.items():
            if isinstance(value, ObjectId):
                # Convert ObjectId to string
                processed[key] = str(value)
            elif isinstance(value, dict):
                # Recursively process nested documents
                processed[key] = self._process_document(value)
            elif isinstance(value, list):
                # Process arrays/lists
                processed[key] = [
                    (
                        self._process_document(item)
                        if isinstance(item, dict)
                        else str(item)
                        if isinstance(item, ObjectId)
                        else item
                    )
                    for item in value
                ]
            else:
                # Keep other types as-is
                processed[key] = value

        return processed


class MongoLoader(Loader):
    """MongoDB 数据加载器。

    实现了MongoDB特定的数据加载逻辑。
    支持批量数据加载和upsert操作。
    自动处理文档转换，确保数据格式兼容性。
    """

    def __init__(self, config: MongoConfig):
        """
        初始化 MongoDB 加载器。

        参数:
            config: MongoDB 配置对象
        """
        self._connector = MongoConnector(config)

    async def prepare_target(self, schema: pa.Schema, table_name: str) -> None:
        """
        准备目标集合（如需要）。

        MongoDB 集合在插入第一个文档时自动创建。
        此方法记录关于 schema 的信息但不创建集合。

        参数:
            schema: 数据的 pyarrow Schema
            table_name: 目标集合名
        """
        # Call parent implementation for logging
        await super().prepare_target(schema, table_name)
        logger.info(
            f"MongoDB prepare_target: Collection '{table_name}' will be created automatically on first insert. "
            "Consider creating indexes manually for better performance."
        )

    async def load_chunk(self, data: pd.DataFrame, import_config: ImportConfig) -> None:
        """
        将 DataFrame 数据块加载到 MongoDB。

        参数:
            data: 要加载的 DataFrame
            import_config: 导入配置对象
        """
        # Early return on empty DataFrame
        if data.empty:
            logger.debug("Empty DataFrame provided, skipping load")
            return

        async with self._connector:
            if self._connector.connection is None or self._connector.database is None:
                raise ConnectionError("MongoDB connection not established")

            table_name = import_config.table_name
            # Get collection
            collection = self._connector.database[table_name]

            # Convert DataFrame to list of documents
            documents = data.to_dict("records")
            strategy = import_config.conflict_strategy

            try:
                if strategy == ConflictStrategy.IGNORE:
                    # For ignore strategy, perform plain insert
                    # MongoDB will handle duplicate key errors automatically
                    result = await collection.insert_many(documents, ordered=False)
                    logger.info(
                        f"Loaded {len(result.inserted_ids)} documents into MongoDB collection '{table_name}' using '{strategy}' strategy."
                    )
                elif strategy == ConflictStrategy.UPSERT:
                    # For upsert strategy, use metadata to determine unique fields
                    base_provider = MongoMetadataProvider(self._connector.config)  # type: ignore
                    metadata_provider = CachedMetadataProvider(base_provider)
                    try:
                        table_metadata = await metadata_provider.get_table_metadata(
                            table_name
                        )

                        # 获取唯一键列（主要是_id）
                        unique_keys = table_metadata.get_unique_key_columns()
                        if unique_keys:
                            unique_fields = unique_keys[0]  # 使用第一个唯一键
                        else:
                            unique_fields = ["_id"]  # 默认使用_id

                        # 执行批量upsert操作
                        operations = []
                        for doc in documents:
                            # 构建查询条件
                            filter_doc = {
                                field: doc.get(field)
                                for field in unique_fields
                                if field in doc
                            }
                            if filter_doc:  # 只有当有唯一字段值时才执行upsert
                                operations.append(
                                    {
                                        "replaceOne": {
                                            "filter": filter_doc,
                                            "replacement": doc,
                                            "upsert": True,
                                        }
                                    }
                                )

                        if operations:
                            result = await collection.bulk_write(
                                operations, ordered=False
                            )
                            logger.info(
                                f"Upserted {result.upserted_count + result.modified_count} documents into MongoDB collection '{table_name}' using '{strategy}' strategy."
                            )
                        else:
                            logger.warning(
                                f"No valid documents for upsert in collection '{table_name}'"
                            )

                    except Exception as e:
                        logger.warning(
                            f"Failed to get metadata for upsert strategy: {e}. Falling back to simple insert."
                        )
                        result = await collection.insert_many(documents, ordered=False)
                        logger.info(
                            f"Loaded {len(result.inserted_ids)} documents into MongoDB collection '{table_name}' using fallback insert."
                        )
                else:
                    # Default to insert
                    result = await collection.insert_many(documents, ordered=False)
                    logger.info(
                        f"Loaded {len(result.inserted_ids)} documents into MongoDB collection '{table_name}' using default insert strategy."
                    )

            except Exception as e:
                logger.error(
                    f"Error loading data into MongoDB collection '{table_name}': {e}"
                )
                raise


class MongoMetadataProvider:
    """MongoDB 元数据查询提供者。

    实现了MongoDB特定的元数据查询逻辑。
    通过采样文档推断集合结构、索引等元数据信息。
    支持完整的集合元数据提取，包括字段信息、索引信息等。
    """

    def __init__(self, config: MongoConfig):
        """
        初始化 MongoDB 元数据查询提供者。

        参数:
            config: MongoDB 配置对象
        """
        self._connector = MongoConnector(config)

    def _infer_column_type_from_value(self, value: Any) -> ColumnType:
        """从值推断列类型"""
        if isinstance(value, bool):
            return ColumnType.BOOLEAN
        elif isinstance(value, int):
            return ColumnType.INTEGER
        elif isinstance(value, float):
            return ColumnType.DOUBLE
        elif isinstance(value, str):
            return ColumnType.VARCHAR
        elif isinstance(value, ObjectId):
            return ColumnType.VARCHAR  # ObjectId作为字符串处理
        elif isinstance(value, dict):
            return ColumnType.JSON
        elif isinstance(value, list):
            return ColumnType.JSON
        else:
            return ColumnType.UNKNOWN

    async def get_table_metadata(
        self, table_name: str, schema_name: str | None = None
    ) -> TableMetadata:
        """获取 MongoDB 集合的元数据信息"""
        async with self._connector:
            collection_name = table_name

            # 检查集合是否存在
            if not await self.table_exists(collection_name, schema_name):
                raise ValueError(
                    f"Collection '{collection_name}' does not exist in database '{self._connector.config.database}'"
                )

            # 查询列信息（通过采样文档推断）
            columns = await self._get_column_info(collection_name)

            # 查询索引信息
            indexes = await self._get_index_info(collection_name)

            # MongoDB没有传统约束，但可以有验证规则
            constraints = await self._get_constraint_info(collection_name)

            return TableMetadata(
                table_name=collection_name,
                database_name=self._connector.config.database,
                columns=columns,
                indexes=indexes,
                constraints=constraints,
            )

    async def _get_column_info(self, collection_name: str) -> list[ColumnInfo]:
        """通过采样文档推断集合的字段信息"""
        db = self._connector.database  # type: AsyncIOMotorDatabase  # type: ignore
        collection = db[collection_name]

        # 采样一些文档来推断字段结构
        sample_size = 100
        documents = []
        async for doc in collection.find().limit(sample_size):
            documents.append(doc)

        if not documents:
            return []

        # 分析所有字段
        field_info = {}
        for doc in documents:
            self._analyze_document_fields(doc, field_info)

        # 转换为ColumnInfo列表
        columns = []
        for field_name, info in field_info.items():
            # 确定最常见的类型
            type_counts = info["types"]
            most_common_type = max(type_counts, key=type_counts.get)

            # 检查是否为_id字段（MongoDB的主键）
            is_primary_key = field_name == "_id"

            columns.append(
                ColumnInfo(
                    name=field_name,
                    data_type=most_common_type,
                    is_nullable=info["null_count"] > 0,
                    is_primary_key=is_primary_key,
                    is_unique=is_primary_key,  # 只有_id是唯一的
                    is_auto_increment=False,  # MongoDB没有自增
                    comment=f"Inferred from {info['total_count']} samples",
                )
            )

        return columns

    def _analyze_document_fields(
        self, doc: dict, field_info: dict, prefix: str = ""
    ) -> None:
        """递归分析文档字段"""
        for key, value in doc.items():
            field_name = f"{prefix}.{key}" if prefix else key

            if field_name not in field_info:
                field_info[field_name] = {
                    "types": {},
                    "null_count": 0,
                    "total_count": 0,
                }

            field_info[field_name]["total_count"] += 1

            if value is None:
                field_info[field_name]["null_count"] += 1
                continue

            # 推断类型
            column_type = self._infer_column_type_from_value(value)

            if column_type not in field_info[field_name]["types"]:
                field_info[field_name]["types"][column_type] = 0
            field_info[field_name]["types"][column_type] += 1

            # 如果是嵌套文档，递归分析（限制深度）
            if isinstance(value, dict) and len(prefix.split(".")) < 2:
                self._analyze_document_fields(value, field_info, field_name)

    async def _get_index_info(self, collection_name: str) -> list[IndexInfo]:
        """查询集合的索引信息"""
        db = self._connector.database  # type: AsyncIOMotorDatabase  # type: ignore
        collection = db[collection_name]

        indexes = []
        index_info = await collection.index_information()

        for index_name, index_spec in index_info.items():
            # 解析索引键
            key_spec = index_spec.get("key", [])
            columns = [field for field, _ in key_spec]

            # 确定索引类型
            is_primary = index_name == "_id_"
            is_unique = index_spec.get("unique", False) or is_primary

            if is_primary:
                index_type = IndexType.PRIMARY
            elif is_unique:
                index_type = IndexType.UNIQUE
            else:
                index_type = IndexType.INDEX

            indexes.append(
                IndexInfo(
                    name=index_name,
                    index_type=index_type,
                    columns=columns,
                    is_unique=is_unique,
                    is_primary=is_primary,
                )
            )

        return indexes

    async def _get_constraint_info(self, collection_name: str) -> list[ConstraintInfo]:
        """查询集合的约束信息（MongoDB约束较少）"""
        # collection_name 参数保留以保持接口一致性
        _ = collection_name  # 标记为已使用
        constraints = []

        # MongoDB主要约束是_id的唯一性，已在索引中处理
        # 这里可以添加验证规则的查询逻辑

        return constraints

    async def table_exists(
        self, table_name: str, schema_name: str | None = None
    ) -> bool:
        """检查集合是否存在"""
        # schema_name 参数保留以保持接口一致性，MongoDB 不使用 schema
        _ = schema_name  # 标记为已使用

        # 直接使用已存在的连接，避免嵌套异步上下文管理器
        db = self._connector.database  # type: AsyncIOMotorDatabase  # type: ignore
        collection_names = await db.list_collection_names()
        return table_name in collection_names

    def get_database_name(self) -> str:
        """获取当前连接的数据库名称"""
        return self._connector.config.database
