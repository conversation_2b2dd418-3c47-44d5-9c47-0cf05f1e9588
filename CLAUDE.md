# AI 助手指南

本文件为 AI 助手在处理此代码库时提供指导。

## 开发命令

### 环境设置

```bash
# 使用 uv 创建并激活虚拟环境
uv venv
source .venv/bin/activate

# 安装依赖
uv pip install -e ".[dev,test,all]"
```

### 代码质量

```bash
# 代码格式化
ruff format src/ tests/

# 代码检查 (Linter 和类型检查)
ruff check src/ tests/
mypy src/
pyright src/

# 安全扫描
bandit -r src/
```

### 测试

```bash
# 运行所有测试
pytest

# 运行特定的测试类别
pytest -m unit                    # 仅单元测试
pytest -m integration            # 仅集成测试
pytest -m "not slow"             # 跳过慢速测试

# 运行并生成覆盖率报告
pytest --cov=src/datax --cov-report=html

# 运行指定的测试文件
pytest tests/unit/test_s3_async.py -v

# 运行单个测试
pytest tests/unit/test_s3_async.py::TestAsyncS3Client::test_upload_file_success -v
```

### 项目管理

```bash
# 构建包
python -m build

# 在开发模式下安装并设置 PYTHONPATH
PYTHONPATH=src python -m datax.cli

# 应用 S3 生命周期策略
apply-s3-lifecycle --bucket my-bucket --config configs/s3_lifecycle.yml
```

## 架构概览

### 项目结构

```bash
.
├──.venv/                      # 由 uv 管理的虚拟环境
├── docs/                       # 项目文档 (例如, 架构决策记录, API 文档)
├── scripts/                    # 辅助脚本 (例如, 部署, 数据初始化)
│   └── apply_s3_lifecycle.py   # 新增：用于应用S3生命周期策略的脚本
├── configs/                    # 新增：存放配置文件的目录
│   └── s3_lifecycle.yml        # 新增：S3生命周期策略配置文件
├── src/                        # 应用主源码目录
│   └── datax/                  # Python 包
│       ├── __init__.py
│       ├── core/               # 核心抽象, 如 Extractor, Translator, Loader
│       ├── connectors/         # 具体的数据库连接器实现
│       ├── flows/              # Prefect 工作流定义
│       ├── models/             # Pydantic 配置模型
│       └── utils/              # 通用工具函数
├── tests/                      # 测试代码
│   ├── __init__.py
│   ├── conftest.py             # Pytest 配置文件和全局 fixtures
│   ├── integration/            # 集成测试
│   └── unit/                   # 单元测试
├── pyproject.toml              # 项目配置的唯一真实来源
└── uv.lock                     # 由 uv 生成的锁定依赖文件
```

### 核心设计模式

这是一个围绕三个核心抽象构建的 ETL/ELT 数据迁移工具包：

- **Extractor** (`src/datax/core/extractor.py`): 从数据库流式提取数据的抽象基类
- **Translator** (`src/datax/core/translator.py`): 可选的数据转换层，默认为 NoOpTranslator
- **Loader** (`src/datax/core/loader.py`): 将数据加载到目标系统的抽象基类

所有核心类都实现了异步上下文管理器，并使用流式/分块处理，以在处理大型数据集时保持内存效率。

### 数据流架构

1.  **提取**: 以可配置的块大小 (cursor_fetch_size) 从源数据库流式传输数据
2.  **转换**: 通过可插拔的转换器管道转换数据块
3.  **加载**: 将处理后的数据块写入目标（Parquet 文件、数据库），并解决冲突

### 关键组件

#### 连接器 (`src/datax/connectors/`)

Extractor 和 Loader 接口的特定数据库实现：

- MySQL (`mysql.py`)
- PostgreSQL (`postgres.py`)
- ClickHouse (`clickhouse.py`)
- MongoDB (`mongo.py`)

#### 配置系统 (`src/datax/models/config.py`)

基于 Pydantic 的配置系统，具有：

- 每个连接器的类型安全数据库配置
- 带有带宽/并发控制的 S3Config
- 用于完整作业定义的 ExportConfig/ImportConfig
- 用于处理数据冲突的 ConflictStrategy 枚举

#### S3 工具 (`src/datax/utils/s3.py`)

全面的异步 S3 操作，具有：

- 带宽限制和并发控制
- 基于信号量的批量上传能力
- AsyncS3Client 上下文管理器
- 基于 YAML 配置的 S3 生命周期策略管理

#### Prefect 集成 (`src/datax/flows/`)

使用 Prefect v3 作为执行引擎的工作流编排。

### 内存管理策略

- **流式处理**: 所有提取器都生成 DataFrame 块，以避免加载整个数据集
- **可配置的块大小**:
  - `cursor_fetch_size`: 数据库游标的批处理大小 (默认: 1000)
  - `parquet_chunk_size`: 写入前在内存中累积的数据大小 (默认: 10,000)
  - `rows_per_file`: 文件拆分的阈值 (默认: 1,000,000)

### 测试架构

- **单元测试**: `tests/unit/` 目录，使用模拟依赖
- **集成测试**: `tests/integration/` 目录，使用 testcontainers 运行真实的数据库实例
- **测试配置**: `tests/conftest.py` 提供 Prefect 测试工具和 PostgreSQL 容器 fixtures

## 开发指南

### AI 交互

- **语言**: 所有交流都使用中文。
- **文档**: 函数文档字符串和参数说明使用中文。
- **注释**: 代码注释使用中文。
- **日志**: 日志消息使用英文。
- **提交**: Git 提交信息使用英文。

### 语言约定

- 使用 Python 3.12+ 及其内置泛型 (例如, 使用 `list` 而不是 `typing.List`)。
- 使用 `uv` 进行虚拟环境和包管理。
- `pyproject.toml` 是项目配置的唯一真实来源。
- Prefect v3 是工作流执行引擎。

### 异步客户端

- **S3**: `aioboto3`
- **MySQL**: `asyncmy`
- **PostgreSQL**: `asyncpg`
- **MongoDB**: `motor`
- **SQLite**: `aiosqlite`
- **ClickHouse**: `clickhouse-connect`
- **本地文件**: `aiofiles`

### 代码标准

- **行长**: 88 个字符 (由 `ruff` 强制执行)。
- **代码检查与格式化**: 使用 `ruff`。
- **类型提示**: 所有函数定义都必须有类型提示。
- **空白**: 禁止出现仅有空白的空行。

### 测试要求

- 使用 `pytest` 和 `testcontainers` 进行集成测试。
- 测试文件必须放在 `tests/` 目录下。
- 编写测试时，应使用相对导入来引用项目模块，而不是从 `src` 开始的绝对路径 (例如, `from datax.core.extractor import Extractor` 是正确的, `from src.datax.core.extractor import Extractor` 是错误的)。
- 在所有数据库和 I/O 操作中，应一致地使用 `async/await` 模式。

### 配置管理

- 所有数据库凭据必须使用 `pydantic.SecretStr` 以防止意外泄露。
- S3 配置应支持带宽限制和并发控制。
- S3 生命周期策略通过 `configs/` 目录下的 YAML 文件进行管理。
