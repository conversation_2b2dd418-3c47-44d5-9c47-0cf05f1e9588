# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Environment Setup
```bash
# Create and activate virtual environment using uv
uv venv
source .venv/bin/activate

# Install dependencies
uv pip install -e ".[dev,test,all]"
```

### Code Quality
```bash
# Run linter and formatter
ruff check src/ tests/
ruff format src/ tests/

# Type checking
mypy src/
pyright src/

# Security scanning
bandit -r src/
```

### Testing
```bash
# Run all tests
pytest

# Run specific test categories
pytest -m unit                    # Unit tests only
pytest -m integration            # Integration tests only
pytest -m "not slow"             # Skip slow tests

# Run tests with coverage
pytest --cov=src/datax --cov-report=html

# Run specific test file
pytest tests/unit/test_s3_async.py -v

# Run single test
pytest tests/unit/test_s3_async.py::TestAsyncS3Client::test_upload_file_success -v
```

### Project Management
```bash
# Build package
python -m build

# Install in development mode with PYTHONPATH
PYTHONPATH=src python -m datax.cli

# Apply S3 lifecycle policies
apply-s3-lifecycle --bucket my-bucket --config configs/s3_lifecycle.yml
```

## Architecture Overview

### Core Design Pattern
This is an ETL/ELT data migration toolkit built around three core abstractions:

- **Extractor** (`src/datax/core/extractor.py`): Abstract base for streaming data extraction from databases
- **Translator** (`src/datax/core/translator.py`): Optional data transformation layer with NoOpTranslator default
- **Loader** (`src/datax/core/loader.py`): Abstract base for loading data into target systems

All core classes implement async context managers and use streaming/chunked processing for memory efficiency with large datasets.

### Data Flow Architecture
1. **Extraction**: Stream data in configurable chunks (cursor_fetch_size) from source databases
2. **Translation**: Transform data chunks through pluggable translator pipeline
3. **Loading**: Write processed chunks to targets (Parquet files, databases) with conflict resolution

### Key Components

#### Connectors (`src/datax/connectors/`)
Database-specific implementations of Extractor and Loader interfaces:
- MySQL (`mysql.py`)
- PostgreSQL (`postgres.py`) 
- ClickHouse (`clickhouse.py`)
- MongoDB (`mongo.py`)

#### Configuration System (`src/datax/models/config.py`)
Pydantic-based configuration with:
- Type-safe database configs per connector
- S3Config with bandwidth/concurrency controls
- ExportConfig/ImportConfig for complete job definitions
- ConflictStrategy enum for handling data conflicts

#### S3 Utilities (`src/datax/utils/s3.py`)
Comprehensive async S3 operations with:
- Bandwidth throttling and concurrency control
- Batch upload capabilities with semaphore-based limiting
- AsyncS3Client context manager
- S3 lifecycle policy management from YAML configs

#### Prefect Integration (`src/datax/flows/`)
Workflow orchestration using Prefect v3 as execution engine.

### Memory Management Strategy
- **Streaming Processing**: All extractors yield DataFrame chunks to avoid loading entire datasets
- **Configurable Chunk Sizes**: 
  - `cursor_fetch_size`: Database cursor batch size (default: 1000)
  - `parquet_chunk_size`: Memory accumulation before write (default: 10,000)
  - `rows_per_file`: File splitting threshold (default: 1,000,000)

### Testing Architecture
- **Unit Tests**: `tests/unit/` with mocked dependencies
- **Integration Tests**: `tests/integration/` using testcontainers for real database instances
- **Test Configuration**: `tests/conftest.py` provides Prefect test harness and PostgreSQL container fixtures

## Development Guidelines

### Language Conventions
- Use Chinese for function docstrings and parameter descriptions
- Use English for log messages and git commit messages
- Use Python 3.13+ built-in generics instead of typing.Type
- Prefect v3 is the workflow execution engine
- Use `uv` for virtual environment management

### Code Standards
- Line length: 160 characters (configured in pyproject.toml)
- Use `ruff` for linting and formatting
- Type hints required for all function definitions
- No empty whitespace-only lines

### Testing Requirements
- Use pytest with testcontainers for integration tests
- Tests must be placed in `tests/` directory
- Add `src/` to PYTHONPATH when running production code
- Use async/await patterns consistently with database operations

### Configuration Management
- All database credentials use SecretStr to prevent logging exposure
- S3 configs support bandwidth limiting and concurrency control
- YAML-based configuration for S3 lifecycle policies in `configs/`