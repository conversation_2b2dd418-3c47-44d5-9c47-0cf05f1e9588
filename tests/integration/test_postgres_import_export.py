#!/usr/bin/env python3

"""
PostgreSQL 数据导入导出集成测试套件。

本模块提供了完整的PostgreSQL数据导入导出功能集成测试，包括：
1. 基础导入导出测试
2. 数据类型兼容性测试
3. 时区处理专项测试
4. 分批导出专项测试
5. 主键冲突处理专项测试

测试环境：
- 使用Docker容器启动PostgreSQL和MinIO服务
- 配置测试用的S3存储桶和数据库连接
- 确保容器在测试开始前完全启动并可用

测试策略：
- 每个测试方法专注一个特定场景
- 为不同测试场景创建独立的测试表，避免测试用例间的数据污染
- 使用pytest框架编写测试用例
- 实现适当的测试数据准备和清理逻辑
- 添加详细的断言验证，确保数据准确性
- 包含异常场景的测试覆盖
"""

import json
import logging
import random
from datetime import UTC, timedelta, timezone
from decimal import Decimal
from typing import Any
from uuid import uuid4

import pytest
from faker import Faker
from sqlalchemy import text

from datax.flows.dump import export_flow
from datax.flows.load import import_flow
from datax.models import (
    ConflictStrategy,
    ExportConfig,
    ImportConfig,
    PostgresConfig,
    QueryConfig,
    S3Config,
)
from datax.utils.s3 import AsyncS3Client

logger = logging.getLogger(__name__)


class TestPostgresImportExport:
    """PostgreSQL 数据导入导出集成测试类"""

    @pytest.fixture(autouse=True)
    async def setup_test_environment(
        self,
        postgres_engine,
        minio_client,
        s3_config: S3Config,
    ):
        """
        设置测试环境，在每个测试方法执行前自动运行。

        功能：
        1. 创建测试用的S3存储桶
        2. 设置测试数据库连接
        3. 准备测试环境变量
        """
        self.postgres_engine = postgres_engine
        self.minio_client = minio_client
        self.s3_config = s3_config

        # 创建测试存储桶
        try:
            await self.minio_client.create_bucket(Bucket=self.s3_config.bucket)
            logger.info(f"Created test bucket: {self.s3_config.bucket}")
        except Exception as e:
            # 桶可能已经存在，忽略错误
            logger.debug(f"Bucket creation failed (may already exist): {e}")

        yield

        # 清理测试环境
        await self._cleanup_test_environment()

    async def _cleanup_test_environment(self):
        """清理测试环境，删除测试数据和S3对象"""
        try:
            # 清理S3对象
            async with AsyncS3Client(self.s3_config) as s3_client:
                async for obj_key in s3_client.list_objects():
                    await s3_client.delete_object(obj_key)
                    logger.debug(f"Deleted S3 object: {obj_key}")
        except Exception as e:
            logger.warning(f"Failed to cleanup S3 objects: {e}")

    async def _list_s3_objects(self):
        """列出S3存储桶中的所有对象"""
        try:
            async with AsyncS3Client(self.s3_config) as s3_client:
                async for obj_key in s3_client.list_objects():
                    yield obj_key
        except Exception as e:
            logger.debug(f"No objects to list or error occurred: {e}")

    async def _create_test_table(self, table_name: str, table_schema: str) -> None:
        """
        创建测试表。

        参数:
            table_name: 表名
            table_schema: 表结构SQL
        """
        # 为每个操作创建新的连接，避免连接状态冲突
        async with self.postgres_engine.connect() as conn:
            async with conn.begin():
                # 删除表如果存在
                await conn.execute(text(f"DROP TABLE IF EXISTS {table_name}"))
                # 创建新表
                await conn.execute(text(table_schema))
                logger.info(f"Created test table: {table_name}")

    async def _insert_test_data(
        self, table_name: str, data: list[dict[str, Any]]
    ) -> None:
        """
        插入测试数据。

        参数:
            table_name: 表名
            data: 要插入的数据列表
        """
        if not data:
            return

        async with self.postgres_engine.connect() as conn:
            async with conn.begin():
                # 构建插入SQL
                columns = list(data[0].keys())
                placeholders = ", ".join([f":{col}" for col in columns])
                sql = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({placeholders})"

                # 批量插入数据
                await conn.execute(text(sql), data)
            logger.info(f"Inserted {len(data)} rows into {table_name}")

    async def _count_table_rows(self, table_name: str) -> int:
        """
        统计表中的行数。

        参数:
            table_name: 表名

        返回:
            int: 行数
        """
        async with self.postgres_engine.connect() as conn:
            result = await conn.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
            return result.scalar()

    async def _fetch_table_data(
        self, table_name: str, order_by: str | None = None
    ) -> list[dict[str, Any]]:
        """
        获取表中的所有数据。

        参数:
            table_name: 表名
            order_by: 排序字段（可选）

        返回:
            list[dict[str, Any]]: 表数据
        """
        sql = f"SELECT * FROM {table_name}"
        if order_by:
            sql += f" ORDER BY {order_by}"

        async with self.postgres_engine.connect() as conn:
            result = await conn.execute(text(sql))
            return [dict(row._mapping) for row in result.fetchall()]

    def _generate_unique_table_name(self, prefix: str = "test_table") -> str:
        """
        生成唯一的表名。

        参数:
            prefix: 表名前缀

        返回:
            str: 唯一表名
        """
        return f"{prefix}_{uuid4().hex[:8]}"

    def _create_s3_key(self, test_name: str) -> str:
        """
        为测试创建唯一的S3键前缀。

        参数:
            test_name: 测试名称

        返回:
            str: S3键前缀
        """
        return f"test/{test_name}_{uuid4().hex[:8]}/"

    # ==================== 测试数据准备工具 ====================

    def _create_basic_test_table_schema(self) -> str:
        """创建基础测试表的SQL架构"""
        return """
        CREATE TABLE {table_name} (
            id SERIAL PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            age INTEGER,
            salary DECIMAL(10,2),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """

    def _generate_basic_test_data(self, count: int = 100) -> list[dict[str, Any]]:
        """
        生成基础测试数据。

        参数:
            count: 生成数据的数量

        返回:
            list[dict[str, Any]]: 测试数据列表
        """
        fake = Faker("zh_CN")
        data = []

        for _ in range(count):
            data.append(
                {
                    "name": fake.name(),
                    "age": random.randint(18, 80),
                    "salary": Decimal(str(random.uniform(3000, 50000))).quantize(
                        Decimal("0.01")
                    ),
                    "is_active": random.choice([True, False]),
                    "created_at": fake.date_time_between(
                        start_date="-1y", end_date="now"
                    ),
                }
            )

        return data

    def _create_comprehensive_data_types_table_schema(self) -> str:
        """创建包含各种数据类型的测试表SQL架构"""
        return """
        CREATE TABLE {table_name} (
            -- 数值类型
            id SERIAL PRIMARY KEY,
            small_int SMALLINT,
            big_int BIGINT,
            decimal_val DECIMAL(15,4),
            float_val REAL,
            double_val DOUBLE PRECISION,

            -- 字符串类型
            varchar_val VARCHAR(255),
            text_val TEXT,
            char_val CHAR(10),

            -- 日期时间类型
            date_val DATE,
            time_val TIME,
            timestamp_val TIMESTAMP,
            timestamptz_val TIMESTAMP WITH TIME ZONE,

            -- 布尔类型
            bool_val BOOLEAN,

            -- 二进制类型
            bytea_val BYTEA,

            -- JSON类型
            json_val JSON,
            jsonb_val JSONB,

            -- 数组类型
            int_array INTEGER[],
            text_array TEXT[],

            -- UUID类型
            uuid_val UUID
        )
        """

    def _generate_comprehensive_test_data(
        self, count: int = 50
    ) -> list[dict[str, Any]]:
        """
        生成包含各种数据类型的测试数据。

        参数:
            count: 生成数据的数量

        返回:
            list[dict[str, Any]]: 测试数据列表
        """
        fake = Faker("zh_CN")
        data = []

        for i in range(count):
            # 生成时区感知的时间戳
            dt_utc = fake.date_time_between(
                start_date="-1y", end_date="now", tzinfo=UTC
            )
            dt_local = fake.date_time_between(start_date="-1y", end_date="now")

            data.append(
                {
                    # 数值类型
                    "small_int": random.randint(-32768, 32767),
                    "big_int": random.randint(-2147483648, 2147483647),
                    "decimal_val": Decimal(
                        str(random.uniform(-999.9999, 999.9999))
                    ).quantize(Decimal("0.0001")),
                    "float_val": random.uniform(-1000.0, 1000.0),
                    "double_val": random.uniform(-10000.0, 10000.0),
                    # 字符串类型
                    "varchar_val": fake.text(max_nb_chars=200),
                    "text_val": fake.text(max_nb_chars=1000),
                    "char_val": fake.lexify(text="??????????"),  # 10个字符
                    # 日期时间类型
                    "date_val": fake.date_between(start_date="-1y", end_date="today"),
                    "time_val": fake.time_object(),
                    "timestamp_val": dt_local,
                    "timestamptz_val": dt_utc,
                    # 布尔类型
                    "bool_val": random.choice([True, False]),
                    # 二进制类型
                    "bytea_val": fake.binary(length=random.randint(10, 100)),
                    # JSON类型
                    "json_val": json.dumps(
                        {
                            "name": fake.name(),
                            "age": random.randint(18, 80),
                            "tags": fake.words(nb=3),
                        }
                    ),
                    "jsonb_val": json.dumps(
                        {
                            "id": i,
                            "data": fake.text(max_nb_chars=100),
                            "metadata": {"created": dt_utc.isoformat()},
                        }
                    ),
                    # 数组类型
                    "int_array": [
                        random.randint(1, 100) for _ in range(random.randint(1, 5))
                    ],
                    "text_array": fake.words(nb=random.randint(2, 6)),
                    # UUID类型
                    "uuid_val": fake.uuid4(),
                }
            )

        return data

    def _create_primary_key_conflict_table_schema(self) -> str:
        """创建用于主键冲突测试的表SQL架构"""
        return """
        CREATE TABLE {table_name} (
            id INTEGER PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            value INTEGER,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """

    def _create_composite_key_conflict_table_schema(self) -> str:
        """创建用于联合主键冲突测试的表SQL架构"""
        return """
        CREATE TABLE {table_name} (
            user_id INTEGER,
            product_id INTEGER,
            quantity INTEGER NOT NULL,
            price DECIMAL(10,2),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (user_id, product_id)
        )
        """

    def _generate_conflict_test_data(
        self, base_count: int = 20, conflict_count: int = 5
    ) -> tuple[list[dict[str, Any]], list[dict[str, Any]]]:
        """
        生成主键冲突测试数据。

        参数:
            base_count: 基础数据数量
            conflict_count: 冲突数据数量

        返回:
            tuple: (基础数据, 冲突数据)
        """
        fake = Faker("zh_CN")

        # 生成基础数据
        base_data = []
        for i in range(1, base_count + 1):
            base_data.append(
                {
                    "id": i,
                    "name": fake.name(),
                    "value": random.randint(1, 1000),
                    "updated_at": fake.date_time_between(
                        start_date="-1y", end_date="-1m"
                    ),
                }
            )

        # 生成冲突数据（使用相同的ID但不同的值）
        conflict_data = []
        conflict_ids = random.sample(
            range(1, base_count + 1), min(conflict_count, base_count)
        )
        for id_val in conflict_ids:
            conflict_data.append(
                {
                    "id": id_val,
                    "name": fake.name(),
                    "value": random.randint(1001, 2000),  # 不同的值范围
                    "updated_at": fake.date_time_between(
                        start_date="-1w", end_date="now"
                    ),
                }
            )

        return base_data, conflict_data

    def _generate_composite_key_conflict_data(
        self, base_count: int = 30, conflict_count: int = 8
    ) -> tuple[list[dict[str, Any]], list[dict[str, Any]]]:
        """
        生成联合主键冲突测试数据。

        参数:
            base_count: 基础数据数量
            conflict_count: 冲突数据数量

        返回:
            tuple: (基础数据, 冲突数据)
        """
        fake = Faker("zh_CN")

        # 生成基础数据
        base_data = []
        used_combinations = set()

        while len(base_data) < base_count:
            user_id = random.randint(1, 10)
            product_id = random.randint(1, 20)
            combination = (user_id, product_id)

            if combination not in used_combinations:
                used_combinations.add(combination)
                base_data.append(
                    {
                        "user_id": user_id,
                        "product_id": product_id,
                        "quantity": random.randint(1, 10),
                        "price": Decimal(str(random.uniform(10.0, 500.0))).quantize(
                            Decimal("0.01")
                        ),
                        "created_at": fake.date_time_between(
                            start_date="-1y", end_date="-1m"
                        ),
                    }
                )

        # 生成冲突数据（使用相同的联合主键但不同的值）
        conflict_data = []
        conflict_combinations = random.sample(
            list(used_combinations), min(conflict_count, len(used_combinations))
        )

        for user_id, product_id in conflict_combinations:
            conflict_data.append(
                {
                    "user_id": user_id,
                    "product_id": product_id,
                    "quantity": random.randint(11, 20),  # 不同的数量范围
                    "price": Decimal(str(random.uniform(501.0, 1000.0))).quantize(
                        Decimal("0.01")
                    ),
                    "created_at": fake.date_time_between(
                        start_date="-1w", end_date="now"
                    ),
                }
            )

        return base_data, conflict_data

    def _create_timezone_test_table_schema(self) -> str:
        """创建用于时区测试的表SQL架构"""
        return """
        CREATE TABLE {table_name} (
            id SERIAL PRIMARY KEY,
            event_name VARCHAR(200) NOT NULL,
            local_time TIMESTAMP,
            utc_time TIMESTAMP WITH TIME ZONE,
            timezone_name VARCHAR(50),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        )
        """

    def _generate_timezone_test_data(self, count: int = 30) -> list[dict[str, Any]]:
        """
        生成时区测试数据。

        参数:
            count: 生成数据的数量

        返回:
            list[dict[str, Any]]: 时区测试数据列表
        """
        fake = Faker("zh_CN")

        # 定义不同的时区
        timezones = [
            ("UTC", UTC),
            ("Asia/Shanghai", timezone(timedelta(hours=8))),
            ("America/New_York", timezone(timedelta(hours=-5))),
            ("Europe/London", timezone(timedelta(hours=0))),
            ("Asia/Tokyo", timezone(timedelta(hours=9))),
        ]

        data = []
        for _ in range(count):
            # 随机选择时区
            tz_name, _ = random.choice(timezones)

            # 生成本地时间（无时区信息）
            local_dt = fake.date_time_between(start_date="-1y", end_date="now")

            # 生成UTC时间（带时区信息）
            utc_dt = fake.date_time_between(
                start_date="-1y", end_date="now", tzinfo=UTC
            )

            data.append(
                {
                    "event_name": fake.sentence(nb_words=4),
                    "local_time": local_dt,
                    "utc_time": utc_dt,
                    "timezone_name": tz_name,
                }
            )

        return data

    # ==================== 基础导入导出测试 ====================

    @pytest.mark.postgres
    @pytest.mark.minio
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_basic_export_import_roundtrip(self, postgres_config: PostgresConfig):
        """
        测试基础的导出导入往返流程。

        测试步骤：
        1. 创建测试表并插入数据
        2. 导出数据到S3
        3. 创建新表
        4. 从S3导入数据到新表
        5. 验证数据完整性
        """
        # 1. 准备测试数据
        source_table = self._generate_unique_table_name("basic_export_source")
        target_table = self._generate_unique_table_name("basic_export_target")
        s3_key = self._create_s3_key("basic_export_import")

        # 创建源表并插入数据
        table_schema = self._create_basic_test_table_schema().format(
            table_name=source_table
        )
        await self._create_test_table(source_table, table_schema)

        test_data = self._generate_basic_test_data(count=50)
        await self._insert_test_data(source_table, test_data)

        # 验证源表数据
        source_count = await self._count_table_rows(source_table)
        assert source_count == 50, (
            f"Expected 50 rows in source table, got {source_count}"
        )

        # 2. 导出数据到S3
        export_config = ExportConfig(
            source_db=postgres_config,
            query=QueryConfig(table_name=source_table),
            s3_target=S3Config(
                endpoint_url=self.s3_config.endpoint_url,
                access_key=self.s3_config.access_key,
                secret_key=self.s3_config.secret_key,
                bucket=self.s3_config.bucket,
                key=s3_key,
                region=self.s3_config.region,
            ),
            parquet_chunk_size=20,  # 小批次用于测试
            rows_per_file=30,  # 小文件用于测试
        )

        await export_flow(export_config)
        logger.info(f"Export completed for table {source_table}")

        # 3. 验证S3中的文件
        s3_objects = []
        async for obj_key in self._list_s3_objects():
            if obj_key.startswith(s3_key) and obj_key.endswith(".parquet"):
                s3_objects.append(obj_key)

        assert len(s3_objects) > 0, "No Parquet files found in S3"
        logger.info(f"Found {len(s3_objects)} Parquet files in S3")

        # 4. 创建目标表
        target_schema = self._create_basic_test_table_schema().format(
            table_name=target_table
        )
        await self._create_test_table(target_table, target_schema)

        # 5. 从S3导入数据
        import_config = ImportConfig(
            table_name=target_table,
            s3_source=S3Config(
                endpoint_url=self.s3_config.endpoint_url,
                access_key=self.s3_config.access_key,
                secret_key=self.s3_config.secret_key,
                bucket=self.s3_config.bucket,
                key=s3_key,
                region=self.s3_config.region,
            ),
            target_db=postgres_config,
            conflict_strategy=ConflictStrategy.IGNORE,
        )

        await import_flow(import_config)
        logger.info(f"Import completed for table {target_table}")

        # 6. 验证数据完整性
        target_count = await self._count_table_rows(target_table)
        assert target_count == source_count, (
            f"Expected {source_count} rows in target table, got {target_count}"
        )

        # 获取源表和目标表数据进行比较
        source_data = await self._fetch_table_data(source_table, order_by="id")
        target_data = await self._fetch_table_data(target_table, order_by="id")

        # 比较数据（排除自动生成的ID字段）
        assert len(source_data) == len(target_data), "Data count mismatch"

        for i, (source_row, target_row) in enumerate(
            zip(source_data, target_data, strict=False)
        ):
            # 比较除ID外的所有字段
            for key in ["name", "age", "salary", "is_active"]:
                source_val = source_row[key]
                target_val = target_row[key]

                # 直接比较值，Python会正确处理Decimal类型
                assert source_val == target_val, (
                    f"Row {i}, field {key}: {source_val} != {target_val}"
                )

        logger.info("Basic export-import roundtrip test completed successfully")

    # ==================== 数据类型兼容性测试 ====================

    @pytest.mark.postgres
    @pytest.mark.minio
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_comprehensive_data_types_compatibility(
        self, postgres_config: PostgresConfig
    ):
        """
        测试各种PostgreSQL数据类型的导入导出兼容性。

        测试数据类型包括：
        - 数值类型：SMALLINT, INTEGER, BIGINT, DECIMAL, REAL, DOUBLE PRECISION
        - 字符串类型：VARCHAR, TEXT, CHAR
        - 日期时间类型：DATE, TIME, TIMESTAMP, TIMESTAMP WITH TIME ZONE
        - 布尔类型：BOOLEAN
        - 二进制类型：BYTEA
        - JSON类型：JSON, JSONB
        - 数组类型：INTEGER[], TEXT[]
        - UUID类型：UUID
        """
        # 1. 准备测试数据
        source_table = self._generate_unique_table_name("datatypes_source")
        target_table = self._generate_unique_table_name("datatypes_target")
        s3_key = self._create_s3_key("datatypes_test")

        # 创建包含各种数据类型的源表
        table_schema = self._create_comprehensive_data_types_table_schema().format(
            table_name=source_table
        )
        await self._create_test_table(source_table, table_schema)

        test_data = self._generate_comprehensive_test_data(count=30)
        await self._insert_test_data(source_table, test_data)

        # 验证源表数据
        source_count = await self._count_table_rows(source_table)
        assert source_count == 30, (
            f"Expected 30 rows in source table, got {source_count}"
        )

        # 2. 导出数据到S3
        export_config = ExportConfig(
            source_db=postgres_config,
            query=QueryConfig(table_name=source_table),
            s3_target=S3Config(
                endpoint_url=self.s3_config.endpoint_url,
                access_key=self.s3_config.access_key,
                secret_key=self.s3_config.secret_key,
                bucket=self.s3_config.bucket,
                key=s3_key,
                region=self.s3_config.region,
            ),
            parquet_chunk_size=15,  # 小批次用于测试
            rows_per_file=20,  # 小文件用于测试
        )

        await export_flow(export_config)
        logger.info(
            f"Export completed for comprehensive data types table {source_table}"
        )

        # 3. 验证S3中的文件
        s3_objects = []
        async for obj_key in self._list_s3_objects():
            if obj_key.startswith(s3_key) and obj_key.endswith(".parquet"):
                s3_objects.append(obj_key)

        assert len(s3_objects) > 0, "No Parquet files found in S3"
        logger.info(f"Found {len(s3_objects)} Parquet files in S3")

        # 4. 创建目标表
        target_schema = self._create_comprehensive_data_types_table_schema().format(
            table_name=target_table
        )
        await self._create_test_table(target_table, target_schema)

        # 5. 从S3导入数据
        import_config = ImportConfig(
            table_name=target_table,
            s3_source=S3Config(
                endpoint_url=self.s3_config.endpoint_url,
                access_key=self.s3_config.access_key,
                secret_key=self.s3_config.secret_key,
                bucket=self.s3_config.bucket,
                key=s3_key,
                region=self.s3_config.region,
            ),
            target_db=postgres_config,
            conflict_strategy=ConflictStrategy.IGNORE,
        )

        await import_flow(import_config)
        logger.info(
            f"Import completed for comprehensive data types table {target_table}"
        )

        # 6. 验证数据完整性
        target_count = await self._count_table_rows(target_table)
        assert target_count == source_count, (
            f"Expected {source_count} rows in target table, got {target_count}"
        )

        # 获取源表和目标表数据进行详细比较
        source_data = await self._fetch_table_data(source_table, order_by="id")
        target_data = await self._fetch_table_data(target_table, order_by="id")

        assert len(source_data) == len(target_data), "Data count mismatch"

        # 详细比较每种数据类型
        for i, (source_row, target_row) in enumerate(
            zip(source_data, target_data, strict=False)
        ):
            self._compare_comprehensive_data_types(source_row, target_row, i)

        logger.info(
            "Comprehensive data types compatibility test completed successfully"
        )

    def _compare_comprehensive_data_types(
        self, source_row: dict[str, Any], target_row: dict[str, Any], row_index: int
    ):
        """
        比较包含各种数据类型的行数据。

        参数:
            source_row: 源行数据
            target_row: 目标行数据
            row_index: 行索引（用于错误报告）
        """
        self._compare_numeric_fields(source_row, target_row, row_index)
        self._compare_string_fields(source_row, target_row, row_index)
        self._compare_datetime_fields(source_row, target_row, row_index)
        self._compare_boolean_fields(source_row, target_row, row_index)
        self._compare_binary_fields(source_row, target_row, row_index)
        self._compare_json_fields(source_row, target_row, row_index)
        self._compare_array_fields(source_row, target_row, row_index)

    def _compare_numeric_fields(
        self, source_row: dict[str, Any], target_row: dict[str, Any], row_index: int
    ):
        """比较数值类型字段"""
        numeric_fields = [
            "small_int",
            "big_int",
            "decimal_val",
            "float_val",
            "double_val",
        ]
        for field in numeric_fields:
            source_val = source_row[field]
            target_val = target_row[field]
            self._compare_single_numeric_field(source_val, target_val, field, row_index)

    def _compare_single_numeric_field(
        self, source_val: Any, target_val: Any, field: str, row_index: int
    ):
        """比较单个数值字段"""
        if source_val is None and target_val is None:
            return

        if self._is_decimal_field(source_val, target_val):
            self._compare_decimal_values(source_val, target_val, field, row_index)
        elif self._is_float_field(source_val, target_val):
            self._compare_float_values(source_val, target_val, field, row_index)
        else:
            self._compare_exact_values(source_val, target_val, field, row_index)

    def _is_decimal_field(self, source_val: Any, target_val: Any) -> bool:
        """检查是否为Decimal字段"""
        return isinstance(source_val, Decimal) or isinstance(target_val, Decimal)

    def _is_float_field(self, source_val: Any, target_val: Any) -> bool:
        """检查是否为float字段"""
        return isinstance(source_val, float) or isinstance(target_val, float)

    def _compare_decimal_values(
        self, source_val: Any, target_val: Any, field: str, row_index: int
    ):
        """比较Decimal值"""
        if source_val is not None and target_val is not None:
            assert abs(Decimal(str(source_val)) - Decimal(str(target_val))) < Decimal(
                "0.0001"
            ), f"Row {row_index}, field {field}: {source_val} != {target_val}"

    def _compare_float_values(
        self, source_val: Any, target_val: Any, field: str, row_index: int
    ):
        """比较float值"""
        if source_val is not None and target_val is not None:
            assert abs(float(source_val) - float(target_val)) < 0.0001, (
                f"Row {row_index}, field {field}: {source_val} != {target_val}"
            )

    def _compare_exact_values(
        self, source_val: Any, target_val: Any, field: str, row_index: int
    ):
        """比较精确值"""
        assert source_val == target_val, (
            f"Row {row_index}, field {field}: {source_val} != {target_val}"
        )

    def _compare_string_fields(
        self, source_row: dict[str, Any], target_row: dict[str, Any], row_index: int
    ):
        """比较字符串类型字段"""
        string_fields = ["varchar_val", "text_val", "char_val"]
        for field in string_fields:
            source_val = source_row[field]
            target_val = target_row[field]

            # CHAR类型可能会有尾随空格，需要特殊处理
            if field == "char_val":
                if source_val is not None:
                    source_val = source_val.rstrip()
                if target_val is not None:
                    target_val = target_val.rstrip()

            assert source_val == target_val, (
                f"Row {row_index}, field {field}: '{source_val}' != '{target_val}'"
            )

    def _compare_datetime_fields(
        self, source_row: dict[str, Any], target_row: dict[str, Any], row_index: int
    ):
        """比较日期时间类型字段"""
        datetime_fields = ["date_val", "time_val", "timestamp_val", "timestamptz_val"]
        for field in datetime_fields:
            source_val = source_row[field]
            target_val = target_row[field]

            if field == "timestamptz_val":
                self._compare_timezone_aware_timestamp(
                    source_val, target_val, field, row_index
                )
            else:
                assert source_val == target_val, (
                    f"Row {row_index}, field {field}: {source_val} != {target_val}"
                )

    def _compare_timezone_aware_timestamp(
        self, source_val: Any, target_val: Any, field: str, row_index: int
    ):
        """比较时区感知的时间戳"""
        if source_val is not None and target_val is not None:
            if hasattr(source_val, "tzinfo") and hasattr(target_val, "tzinfo"):
                source_utc = (
                    source_val.astimezone(UTC)
                    if source_val.tzinfo
                    else source_val.replace(tzinfo=UTC)
                )
                target_utc = (
                    target_val.astimezone(UTC)
                    if target_val.tzinfo
                    else target_val.replace(tzinfo=UTC)
                )
                assert abs((source_utc - target_utc).total_seconds()) < 1, (
                    f"Row {row_index}, field {field}: {source_val} != {target_val}"
                )
            else:
                assert source_val == target_val, (
                    f"Row {row_index}, field {field}: {source_val} != {target_val}"
                )

    def _compare_boolean_fields(
        self, source_row: dict[str, Any], target_row: dict[str, Any], row_index: int
    ):
        """比较布尔类型字段"""
        assert source_row["bool_val"] == target_row["bool_val"], (
            f"Row {row_index}, bool_val: {source_row['bool_val']} != {target_row['bool_val']}"
        )

    def _compare_binary_fields(
        self, source_row: dict[str, Any], target_row: dict[str, Any], row_index: int
    ):
        """比较二进制类型字段"""
        source_bytea = source_row["bytea_val"]
        target_bytea = target_row["bytea_val"]

        if source_bytea is not None and target_bytea is not None:
            if isinstance(source_bytea, str):
                source_bytea = source_bytea.encode("utf-8")
            if isinstance(target_bytea, str):
                target_bytea = target_bytea.encode("utf-8")
            assert source_bytea == target_bytea, (
                f"Row {row_index}, bytea_val: binary data mismatch"
            )
        else:
            assert source_bytea == target_bytea, (
                f"Row {row_index}, bytea_val: {source_bytea} != {target_bytea}"
            )

    def _compare_json_fields(
        self, source_row: dict[str, Any], target_row: dict[str, Any], row_index: int
    ):
        """比较JSON类型字段"""
        json_fields = ["json_val", "jsonb_val"]
        for field in json_fields:
            source_json = source_row[field]
            target_json = target_row[field]

            if source_json is not None and target_json is not None:
                source_obj = (
                    json.loads(source_json)
                    if isinstance(source_json, str)
                    else source_json
                )
                target_obj = (
                    json.loads(target_json)
                    if isinstance(target_json, str)
                    else target_json
                )

                assert source_obj == target_obj, (
                    f"Row {row_index}, field {field}: {source_obj} != {target_obj}"
                )
            else:
                assert source_json == target_json, (
                    f"Row {row_index}, field {field}: {source_json} != {target_json}"
                )

    def _compare_array_fields(
        self, source_row: dict[str, Any], target_row: dict[str, Any], row_index: int
    ):
        """比较数组类型字段"""
        array_fields = ["int_array", "text_array"]
        for field in array_fields:
            source_array = source_row[field]
            target_array = target_row[field]
            self._compare_single_array_field(
                source_array, target_array, field, row_index
            )

    def _compare_single_array_field(
        self, source_array: Any, target_array: Any, field: str, row_index: int
    ):
        """比较单个数组字段"""
        if source_array is not None and target_array is not None:
            normalized_source = self._normalize_array_value(source_array)
            normalized_target = self._normalize_array_value(target_array)
            self._assert_arrays_equal(
                normalized_source, normalized_target, field, row_index
            )
        else:
            self._assert_arrays_equal(source_array, target_array, field, row_index)

    def _normalize_array_value(self, array_value: Any) -> Any:
        """标准化数组值格式"""
        if isinstance(array_value, str):
            return eval(array_value) if array_value.startswith("[") else [array_value]
        return array_value

    def _assert_arrays_equal(
        self, source_array: Any, target_array: Any, field: str, row_index: int
    ):
        """断言数组相等"""
        assert source_array == target_array, (
            f"Row {row_index}, field {field}: {source_array} != {target_array}"
        )

    # ==================== 时区处理专项测试 ====================

    @pytest.mark.postgres
    @pytest.mark.minio
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_timezone_handling_comprehensive(
        self, postgres_config: PostgresConfig
    ):
        """
        测试时区处理的综合场景。

        测试内容：
        1. TIMESTAMP WITH TIME ZONE字段的正确处理
        2. 不同时区数据的导入导出准确性
        3. UTC时间与本地时间的转换
        4. 时区信息的保持
        """
        # 1. 准备测试数据
        source_table = self._generate_unique_table_name("timezone_source")
        target_table = self._generate_unique_table_name("timezone_target")
        s3_key = self._create_s3_key("timezone_test")

        # 创建时区测试表
        table_schema = self._create_timezone_test_table_schema().format(
            table_name=source_table
        )
        await self._create_test_table(source_table, table_schema)

        test_data = self._generate_timezone_test_data(count=25)
        await self._insert_test_data(source_table, test_data)

        # 验证源表数据
        source_count = await self._count_table_rows(source_table)
        assert source_count == 25, (
            f"Expected 25 rows in source table, got {source_count}"
        )

        # 2. 导出数据到S3
        export_config = ExportConfig(
            source_db=postgres_config,
            query=QueryConfig(table_name=source_table),
            s3_target=S3Config(
                endpoint_url=self.s3_config.endpoint_url,
                access_key=self.s3_config.access_key,
                secret_key=self.s3_config.secret_key,
                bucket=self.s3_config.bucket,
                key=s3_key,
                region=self.s3_config.region,
            ),
            parquet_chunk_size=10,
            rows_per_file=15,
        )

        await export_flow(export_config)
        logger.info(f"Timezone export completed for table {source_table}")

        # 3. 验证S3中的文件
        s3_objects = []
        async for obj_key in self._list_s3_objects():
            if obj_key.startswith(s3_key) and obj_key.endswith(".parquet"):
                s3_objects.append(obj_key)

        assert len(s3_objects) > 0, "No Parquet files found in S3"
        logger.info(f"Found {len(s3_objects)} Parquet files in S3")

        # 4. 创建目标表
        target_schema = self._create_timezone_test_table_schema().format(
            table_name=target_table
        )
        await self._create_test_table(target_table, target_schema)

        # 5. 从S3导入数据
        import_config = ImportConfig(
            table_name=target_table,
            s3_source=S3Config(
                endpoint_url=self.s3_config.endpoint_url,
                access_key=self.s3_config.access_key,
                secret_key=self.s3_config.secret_key,
                bucket=self.s3_config.bucket,
                key=s3_key,
                region=self.s3_config.region,
            ),
            target_db=postgres_config,
            conflict_strategy=ConflictStrategy.IGNORE,
        )

        await import_flow(import_config)
        logger.info(f"Timezone import completed for table {target_table}")

        # 6. 验证时区数据完整性
        target_count = await self._count_table_rows(target_table)
        assert target_count == source_count, (
            f"Expected {source_count} rows in target table, got {target_count}"
        )

        # 获取源表和目标表数据进行时区特定比较
        source_data = await self._fetch_table_data(source_table, order_by="id")
        target_data = await self._fetch_table_data(target_table, order_by="id")

        assert len(source_data) == len(target_data), "Data count mismatch"

        # 详细比较时区相关字段
        for i, (source_row, target_row) in enumerate(
            zip(source_data, target_data, strict=False)
        ):
            self._compare_timezone_specific_fields(source_row, target_row, i)

        logger.info("Timezone handling comprehensive test completed successfully")

    def _compare_timezone_specific_fields(
        self, source_row: dict[str, Any], target_row: dict[str, Any], row_index: int
    ):
        """
        比较时区相关字段。

        参数:
            source_row: 源行数据
            target_row: 目标行数据
            row_index: 行索引（用于错误报告）
        """
        # 比较事件名称
        assert source_row["event_name"] == target_row["event_name"], (
            f"Row {row_index}, event_name: '{source_row['event_name']}' != '{target_row['event_name']}'"
        )

        # 比较本地时间（无时区信息）
        source_local = source_row["local_time"]
        target_local = target_row["local_time"]
        assert source_local == target_local, (
            f"Row {row_index}, local_time: {source_local} != {target_local}"
        )

        # 比较UTC时间（带时区信息）
        source_utc = source_row["utc_time"]
        target_utc = target_row["utc_time"]

        if source_utc is not None and target_utc is not None:
            # 确保两个时间戳都是时区感知的
            if hasattr(source_utc, "tzinfo") and hasattr(target_utc, "tzinfo"):
                # 转换为UTC进行比较
                source_utc_normalized = (
                    source_utc.astimezone(UTC)
                    if source_utc.tzinfo
                    else source_utc.replace(tzinfo=UTC)
                )
                target_utc_normalized = (
                    target_utc.astimezone(UTC)
                    if target_utc.tzinfo
                    else target_utc.replace(tzinfo=UTC)
                )

                # 允许1秒的误差
                time_diff = abs(
                    (source_utc_normalized - target_utc_normalized).total_seconds()
                )
                assert time_diff < 1, (
                    f"Row {row_index}, utc_time: {source_utc} != {target_utc} (diff: {time_diff}s)"
                )
            else:
                # 如果没有时区信息，直接比较
                assert source_utc == target_utc, (
                    f"Row {row_index}, utc_time: {source_utc} != {target_utc}"
                )
        else:
            assert source_utc == target_utc, (
                f"Row {row_index}, utc_time: {source_utc} != {target_utc}"
            )

        # 比较时区名称
        assert source_row["timezone_name"] == target_row["timezone_name"], (
            f"Row {row_index}, timezone_name: '{source_row['timezone_name']}' != '{target_row['timezone_name']}'"
        )

    # ==================== 分批导出专项测试 ====================

    @pytest.mark.postgres
    @pytest.mark.minio
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_batch_export_large_dataset(self, postgres_config: PostgresConfig):
        """
        测试大数据集分批导出为多个Parquet文件。

        测试内容：
        1. 大数据集分批导出为多个Parquet文件
        2. 验证所有批次数据完整性
        3. 确保无数据丢失
        4. 测试不同批次大小配置的影响
        """
        # 1. 准备大量测试数据
        source_table = self._generate_unique_table_name("batch_export_source")
        target_table = self._generate_unique_table_name("batch_export_target")
        s3_key = self._create_s3_key("batch_export_test")

        # 创建基础测试表
        table_schema = self._create_basic_test_table_schema().format(
            table_name=source_table
        )
        await self._create_test_table(source_table, table_schema)

        # 生成大量测试数据（500条记录）
        test_data = self._generate_basic_test_data(count=500)
        await self._insert_test_data(source_table, test_data)

        # 验证源表数据
        source_count = await self._count_table_rows(source_table)
        assert source_count == 500, (
            f"Expected 500 rows in source table, got {source_count}"
        )

        # 2. 配置小批次导出以强制分批
        export_config = ExportConfig(
            source_db=postgres_config,
            query=QueryConfig(table_name=source_table),
            s3_target=S3Config(
                endpoint_url=self.s3_config.endpoint_url,
                access_key=self.s3_config.access_key,
                secret_key=self.s3_config.secret_key,
                bucket=self.s3_config.bucket,
                key=s3_key,
                region=self.s3_config.region,
            ),
            parquet_chunk_size=50,  # 小批次大小
            rows_per_file=100,  # 每个文件100行，应该产生5个文件
            cursor_fetch_size=75,  # 游标获取大小
        )

        await export_flow(export_config)
        logger.info(f"Batch export completed for table {source_table}")

        # 3. 验证S3中的多个Parquet文件
        s3_objects = []
        async for obj_key in self._list_s3_objects():
            if obj_key.startswith(s3_key) and obj_key.endswith(".parquet"):
                s3_objects.append(obj_key)

        # 应该有多个文件
        assert len(s3_objects) >= 5, (
            f"Expected at least 5 Parquet files, got {len(s3_objects)}"
        )
        logger.info(f"Found {len(s3_objects)} Parquet files in S3")

        # 4. 创建目标表并导入所有数据
        target_schema = self._create_basic_test_table_schema().format(
            table_name=target_table
        )
        await self._create_test_table(target_table, target_schema)

        # 5. 从S3导入所有批次数据
        import_config = ImportConfig(
            table_name=target_table,
            s3_source=S3Config(
                endpoint_url=self.s3_config.endpoint_url,
                access_key=self.s3_config.access_key,
                secret_key=self.s3_config.secret_key,
                bucket=self.s3_config.bucket,
                key=s3_key,
                region=self.s3_config.region,
            ),
            target_db=postgres_config,
            conflict_strategy=ConflictStrategy.IGNORE,
        )

        await import_flow(import_config)
        logger.info(f"Batch import completed for table {target_table}")

        # 6. 验证数据完整性
        target_count = await self._count_table_rows(target_table)
        assert target_count == source_count, (
            f"Expected {source_count} rows in target table, got {target_count}"
        )

        # 7. 验证数据内容一致性（抽样检查前50行）
        source_sample = await self._fetch_table_data(source_table, order_by="id")
        target_sample = await self._fetch_table_data(target_table, order_by="id")

        assert len(source_sample) == len(target_sample), "Sample data count mismatch"

        # 只检查前50行作为抽样
        sample_size = min(50, len(source_sample))
        for i in range(sample_size):
            source_row = source_sample[i]
            target_row = target_sample[i]
            # 比较关键字段
            for key in ["name", "age", "salary", "is_active"]:
                assert source_row[key] == target_row[key], (
                    f"Sample row {i}, field {key}: {source_row[key]} != {target_row[key]}"
                )

        logger.info("Batch export large dataset test completed successfully")

    # ==================== 主键冲突处理专项测试 ====================

    @pytest.mark.postgres
    @pytest.mark.minio
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_primary_key_conflict_strategies(
        self, postgres_config: PostgresConfig
    ):
        """
        测试单一主键冲突场景和不同的冲突处理策略。

        测试内容：
        1. 测试IGNORE策略（跳过冲突记录）
        2. 测试REPLACE策略（覆盖冲突记录）
        3. 测试UPSERT策略（更新或插入）
        4. 验证冲突处理策略按预期工作
        """
        # 1. 准备测试数据
        target_table = self._generate_unique_table_name("pk_conflict_target")
        s3_key = self._create_s3_key("pk_conflict_test")

        # 创建主键冲突测试表
        table_schema = self._create_primary_key_conflict_table_schema().format(
            table_name=target_table
        )
        await self._create_test_table(target_table, table_schema)

        # 插入初始数据
        initial_data = [
            {
                "id": 1,
                "name": "Original User 1",
                "value": 100,
            },
            {
                "id": 2,
                "name": "Original User 2",
                "value": 200,
            },
            {
                "id": 3,
                "name": "Original User 3",
                "value": 300,
            },
        ]
        await self._insert_test_data(target_table, initial_data)

        # 验证初始数据
        initial_count = await self._count_table_rows(target_table)
        assert initial_count == 3, f"Expected 3 initial rows, got {initial_count}"

        # 2. 准备冲突数据（包含相同主键但不同内容的记录）
        # 生成与初始数据冲突的数据（ID 1,2,3）+ 一些新数据（ID 4,5）
        conflict_data = [
            # 与初始数据冲突的记录
            {"id": 1, "name": "Updated User 1", "value": 1001},
            {"id": 2, "name": "Updated User 2", "value": 1002},
            # 新的记录
            {"id": 4, "name": "New User 4", "value": 400},
            {"id": 5, "name": "New User 5", "value": 500},
        ]

        # 将冲突数据导出到S3（模拟从另一个源导入的场景）
        temp_source_table = self._generate_unique_table_name("pk_conflict_source")
        temp_schema = self._create_primary_key_conflict_table_schema().format(
            table_name=temp_source_table
        )
        await self._create_test_table(temp_source_table, temp_schema)
        await self._insert_test_data(temp_source_table, conflict_data)

        # 导出冲突数据到S3
        export_config = ExportConfig(
            source_db=postgres_config,
            query=QueryConfig(table_name=temp_source_table),
            s3_target=S3Config(
                endpoint_url=self.s3_config.endpoint_url,
                access_key=self.s3_config.access_key,
                secret_key=self.s3_config.secret_key,
                bucket=self.s3_config.bucket,
                key=s3_key,
                region=self.s3_config.region,
            ),
        )

        await export_flow(export_config)
        logger.info("Conflict data exported to S3")

        # 3. 测试IGNORE策略
        await self._test_conflict_strategy(
            postgres_config,
            target_table,
            s3_key,
            ConflictStrategy.IGNORE,
            expected_count=5,  # 3个原始 + 2个新的（冲突的被忽略）
            strategy_name="IGNORE",
        )

        # 重置表数据
        await self._reset_table_data(target_table, initial_data)

        # 4. 测试REPLACE策略
        await self._test_conflict_strategy(
            postgres_config,
            target_table,
            s3_key,
            ConflictStrategy.REPLACE,
            expected_count=5,  # 3个被替换 + 2个新的
            strategy_name="REPLACE",
        )

        # 重置表数据
        await self._reset_table_data(target_table, initial_data)

        # 5. 测试UPSERT策略
        await self._test_conflict_strategy(
            postgres_config,
            target_table,
            s3_key,
            ConflictStrategy.UPSERT,
            expected_count=5,  # 3个被更新 + 2个新的
            strategy_name="UPSERT",
        )

        logger.info("Primary key conflict strategies test completed successfully")

    async def _test_conflict_strategy(
        self,
        postgres_config: PostgresConfig,
        target_table: str,
        s3_key: str,
        strategy: ConflictStrategy,
        expected_count: int,
        strategy_name: str,
    ):
        """测试特定的冲突处理策略"""
        import_config = ImportConfig(
            table_name=target_table,
            s3_source=S3Config(
                endpoint_url=self.s3_config.endpoint_url,
                access_key=self.s3_config.access_key,
                secret_key=self.s3_config.secret_key,
                bucket=self.s3_config.bucket,
                key=s3_key,
                region=self.s3_config.region,
            ),
            target_db=postgres_config,
            conflict_strategy=strategy,
        )

        await import_flow(import_config)
        logger.info(f"{strategy_name} strategy import completed")

        # 验证记录数量
        final_count = await self._count_table_rows(target_table)
        assert final_count == expected_count, (
            f"{strategy_name} strategy: Expected {expected_count} rows, got {final_count}"
        )

        # 验证数据内容（根据策略不同，验证逻辑也不同）
        final_data = await self._fetch_table_data(target_table, order_by="id")

        if strategy == ConflictStrategy.IGNORE:
            # IGNORE策略：原始数据应该保持不变
            original_records = [row for row in final_data if row["id"] in [1, 2, 3]]
            for record in original_records:
                assert record["name"].startswith("Original"), (
                    f"IGNORE: Original record was modified: {record}"
                )

        elif strategy == ConflictStrategy.REPLACE:
            # REPLACE策略：冲突记录应该被替换
            replaced_records = [row for row in final_data if row["id"] in [1, 2]]
            for record in replaced_records:
                assert record["name"].startswith("Updated"), (
                    f"REPLACE: Record was not replaced: {record}"
                )

        elif strategy == ConflictStrategy.UPSERT:
            # UPSERT策略：冲突记录应该被更新
            upserted_records = [row for row in final_data if row["id"] in [1, 2]]
            for record in upserted_records:
                assert record["name"].startswith("Updated"), (
                    f"UPSERT: Record was not upserted: {record}"
                )

        logger.info(f"{strategy_name} strategy validation completed")

    async def _reset_table_data(
        self, table_name: str, initial_data: list[dict[str, Any]]
    ):
        """重置表数据到初始状态"""
        # 清空表
        async with self.postgres_engine.begin() as conn:
            await conn.execute(text(f"DELETE FROM {table_name}"))

        # 重新插入初始数据
        await self._insert_test_data(table_name, initial_data)
        logger.info(f"Table {table_name} reset to initial state")

    @pytest.mark.postgres
    @pytest.mark.minio
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_composite_key_conflict_handling(
        self, postgres_config: PostgresConfig
    ):
        """
        测试联合主键冲突场景。

        测试内容：
        1. 测试联合主键的冲突检测
        2. 验证联合主键冲突处理策略
        3. 确保联合主键约束正确工作
        """
        # 1. 准备测试数据
        target_table = self._generate_unique_table_name("composite_pk_target")
        s3_key = self._create_s3_key("composite_pk_test")

        # 创建联合主键测试表
        table_schema = self._create_composite_key_conflict_table_schema().format(
            table_name=target_table
        )
        await self._create_test_table(target_table, table_schema)

        # 插入初始数据
        initial_data = [
            {
                "user_id": 1,
                "product_id": 101,
                "quantity": 5,
                "price": 99.99,
            },
            {
                "user_id": 1,
                "product_id": 102,
                "quantity": 3,
                "price": 149.99,
            },
            {
                "user_id": 2,
                "product_id": 101,
                "quantity": 2,
                "price": 99.99,
            },
        ]
        await self._insert_test_data(target_table, initial_data)

        # 验证初始数据
        initial_count = await self._count_table_rows(target_table)
        assert initial_count == 3, f"Expected 3 initial rows, got {initial_count}"

        # 2. 准备联合主键冲突数据
        # 生成与初始数据冲突的数据 + 一些新数据
        conflict_data = [
            # 与初始数据冲突的记录
            {"user_id": 1, "product_id": 101, "quantity": 10, "price": 199.99},
            {"user_id": 1, "product_id": 102, "quantity": 8, "price": 299.99},
            # 新的记录
            {"user_id": 2, "product_id": 102, "quantity": 4, "price": 149.99},
            {"user_id": 3, "product_id": 101, "quantity": 1, "price": 99.99},
        ]

        # 导出冲突数据到S3
        temp_source_table = self._generate_unique_table_name("composite_pk_source")
        temp_schema = self._create_composite_key_conflict_table_schema().format(
            table_name=temp_source_table
        )
        await self._create_test_table(temp_source_table, temp_schema)
        await self._insert_test_data(temp_source_table, conflict_data)

        export_config = ExportConfig(
            source_db=postgres_config,
            query=QueryConfig(table_name=temp_source_table),
            s3_target=S3Config(
                endpoint_url=self.s3_config.endpoint_url,
                access_key=self.s3_config.access_key,
                secret_key=self.s3_config.secret_key,
                bucket=self.s3_config.bucket,
                key=s3_key,
                region=self.s3_config.region,
            ),
        )

        await export_flow(export_config)
        logger.info("Composite key conflict data exported to S3")

        # 3. 测试IGNORE策略
        import_config = ImportConfig(
            table_name=target_table,
            s3_source=S3Config(
                endpoint_url=self.s3_config.endpoint_url,
                access_key=self.s3_config.access_key,
                secret_key=self.s3_config.secret_key,
                bucket=self.s3_config.bucket,
                key=s3_key,
                region=self.s3_config.region,
            ),
            target_db=postgres_config,
            conflict_strategy=ConflictStrategy.IGNORE,
        )

        await import_flow(import_config)
        logger.info("Composite key conflict import completed")

        # 4. 验证联合主键冲突处理
        final_count = await self._count_table_rows(target_table)
        # 应该有原始3条 + 新增2条（冲突的1条被忽略）= 5条
        assert final_count == 5, (
            f"Expected 5 rows after composite key conflict handling, got {final_count}"
        )

        final_data = await self._fetch_table_data(
            target_table, order_by="user_id, product_id"
        )

        # 验证冲突记录被正确忽略（原始数据保持不变）
        original_record = next(
            (
                row
                for row in final_data
                if row["user_id"] == 1 and row["product_id"] == 101
            ),
            None,
        )
        assert original_record is not None, "Original composite key record not found"
        assert original_record["quantity"] == 5, (
            f"Original quantity should be 5, got {original_record['quantity']}"
        )

        logger.info("Composite key conflict handling test completed successfully")

    # ==================== 测试套件总结和工具方法 ====================

    def test_suite_summary(self):
        """
        测试套件总结信息。

        本测试套件包含以下测试场景：
        1. 基础导入导出往返测试
        2. 数据类型兼容性测试
        3. 时区处理专项测试
        4. 分批导出专项测试
        5. 主键冲突处理专项测试
        6. 联合主键冲突处理测试

        测试覆盖：
        - PostgreSQL所有主要数据类型
        - 时区感知的时间戳处理
        - 大数据集分批处理
        - 各种冲突处理策略
        - S3存储集成
        - Prefect工作流集成
        """
        logger.info("PostgreSQL Import/Export Integration Test Suite")
        logger.info("=" * 60)
        logger.info("Test Coverage:")
        logger.info("- Basic roundtrip import/export")
        logger.info("- Comprehensive data types compatibility")
        logger.info("- Timezone handling for TIMESTAMP WITH TIME ZONE")
        logger.info("- Batch export for large datasets")
        logger.info("- Primary key conflict strategies (IGNORE, REPLACE, UPSERT)")
        logger.info("- Composite primary key conflict handling")
        logger.info("- S3 storage integration with MinIO")
        logger.info("- Prefect workflow orchestration")
        logger.info("=" * 60)
