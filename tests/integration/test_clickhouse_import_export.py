#!/usr/bin/env python3

"""
ClickHouse 数据导入导出集成测试套件。

本模块提供了完整的ClickHouse数据导入导出功能集成测试，包括：
1. 基础导入导出测试
2. 数据类型兼容性测试
3. 分批导出专项测试
4. 主键冲突处理专项测试

测试环境：
- 使用Docker容器启动ClickHouse和MinIO服务
- 配置测试用的S3存储桶和数据库连接
- 确保容器在测试开始前完全启动并可用

测试策略：
- 每个测试方法专注一个特定场景
- 为不同测试场景创建独立的测试表，避免测试用例间的数据污染
- 使用pytest框架编写测试用例
- 实现适当的测试数据准备和清理逻辑
- 添加详细的断言验证，确保数据准确性
- 包含异常场景的测试覆盖
"""

import logging
import random
from datetime import UTC, datetime
from decimal import Decimal
from typing import Any
from uuid import uuid4

import pandas as pd
import pytest
from clickhouse_connect.driver.asyncclient import AsyncClient
from faker import Faker

from datax.connectors.clickhouse import (
    ClickHouseExtractor,
    ClickHouseLoader,
    ClickHouseMetadataProvider,
)
from datax.models import (
    ClickHouseConfig,
    ConflictStrategy,
    ExportConfig,
    ImportConfig,
    QueryConfig,
    S3Config,
)
from datax.utils.s3 import AsyncS3Client

logger = logging.getLogger(__name__)


class TestClickHouseImportExport:
    """ClickHouse 数据导入导出集成测试类"""

    @pytest.fixture(autouse=True)
    async def setup_test_environment(
        self,
        clickhouse_client: AsyncClient,
        minio_client,
        s3_config: S3Config,
    ):
        """
        设置测试环境，在每个测试方法执行前自动运行。

        功能：
        1. 创建测试用的S3存储桶
        2. 设置测试数据库连接
        3. 准备测试环境变量
        """
        self.clickhouse_client = clickhouse_client
        self.minio_client = minio_client
        self.s3_config = s3_config

        # 创建测试存储桶
        try:
            await self.minio_client.create_bucket(Bucket=self.s3_config.bucket)
            logger.info(f"Created test bucket: {self.s3_config.bucket}")
        except Exception as e:
            # 桶可能已经存在，忽略错误
            logger.debug(f"Bucket creation failed (may already exist): {e}")

        yield

        # 清理测试环境
        await self._cleanup_test_environment()

    async def _cleanup_test_environment(self):
        """清理测试环境，删除测试数据和S3对象"""
        try:
            # 清理S3对象
            async with AsyncS3Client(self.s3_config) as s3_client:
                async for obj_key in s3_client.list_objects():
                    await s3_client.delete_object(obj_key)
                    logger.debug(f"Deleted S3 object: {obj_key}")
        except Exception as e:
            logger.warning(f"Failed to cleanup S3 objects: {e}")

    async def _list_s3_objects(self):
        """列出S3存储桶中的所有对象"""
        try:
            async with AsyncS3Client(self.s3_config) as s3_client:
                async for obj_key in s3_client.list_objects():
                    yield obj_key
        except Exception as e:
            logger.debug(f"No objects to list or error occurred: {e}")

    async def _create_test_table(self, table_name: str, table_schema: str) -> None:
        """
        创建测试表。

        参数:
            table_name: 表名
            table_schema: 表结构SQL
        """
        # 删除表如果存在
        await self.clickhouse_client.command(f"DROP TABLE IF EXISTS {table_name}")
        # 创建新表
        await self.clickhouse_client.command(table_schema)
        logger.info(f"Created test table: {table_name}")

    async def _insert_test_data(
        self, table_name: str, data: list[dict[str, Any]]
    ) -> None:
        """
        插入测试数据。

        参数:
            table_name: 表名
            data: 要插入的数据列表
        """
        if not data:
            return

        # Convert data to DataFrame for batch insert
        df = pd.DataFrame(data)
        await self.clickhouse_client.insert_df(table_name, df)
        logger.info(f"Inserted {len(data)} rows into {table_name}")

    async def _count_table_rows(self, table_name: str) -> int:
        """
        统计表中的行数。

        参数:
            table_name: 表名

        返回:
            int: 行数
        """
        result = await self.clickhouse_client.query(
            f"SELECT COUNT(*) FROM {table_name}"
        )
        return result.result_rows[0][0]

    async def _fetch_table_data(
        self, table_name: str, order_by: str | None = None
    ) -> list[dict[str, Any]]:
        """
        获取表中的所有数据。

        参数:
            table_name: 表名
            order_by: 排序字段（可选）

        返回:
            list[dict[str, Any]]: 表数据
        """
        query = f"SELECT * FROM {table_name}"
        if order_by:
            query += f" ORDER BY {order_by}"

        result = await self.clickhouse_client.query(query)
        columns = result.column_names
        return [dict(zip(columns, row)) for row in result.result_rows]

    def _generate_unique_table_name(self, prefix: str = "test_table") -> str:
        """
        生成唯一的表名。

        参数:
            prefix: 表名前缀

        返回:
            str: 唯一表名
        """
        return f"{prefix}_{uuid4().hex[:8]}"

    def _create_s3_key(self, test_name: str) -> str:
        """
        为测试创建唯一的S3键前缀。

        参数:
            test_name: 测试名称

        返回:
            str: S3键前缀
        """
        return f"test/{test_name}_{uuid4().hex[:8]}/"

    # ==================== 测试数据准备工具 ====================

    def _create_basic_test_table_schema(self, table_name: str) -> str:
        """创建基础测试表的SQL架构"""
        return f"""
        CREATE TABLE {table_name} (
            id UInt32,
            name String,
            age UInt8,
            salary Decimal64(2),
            is_active Bool,
            created_at DateTime
        )
        ENGINE = MergeTree()
        ORDER BY id
        """

    def _generate_basic_test_data(self, count: int = 100) -> list[dict[str, Any]]:
        """
        生成基础测试数据。

        参数:
            count: 生成数据的数量

        返回:
            list[dict[str, Any]]: 测试数据列表
        """
        fake = Faker("zh_CN")
        data = []

        for i in range(count):
            data.append(
                {
                    "id": i + 1,
                    "name": fake.name(),
                    "age": random.randint(18, 80),
                    "salary": Decimal(str(random.uniform(3000, 50000))).quantize(
                        Decimal("0.01")
                    ),
                    "is_active": random.choice([True, False]),
                    "created_at": fake.date_time_between(
                        start_date="-1y", end_date="now"
                    ),
                }
            )

        return data

    def _create_comprehensive_data_types_table_schema(self, table_name: str) -> str:
        """创建包含各种数据类型的测试表SQL架构"""
        return f"""
        CREATE TABLE {table_name} (
            -- 数值类型
            id UInt32,
            small_int Int16,
            big_int Int64,
            decimal_val Decimal128(4),
            float_val Float32,
            double_val Float64,

            -- 字符串类型
            varchar_val String,
            fixed_string_val FixedString(10),

            -- 日期时间类型
            date_val Date,
            datetime_val DateTime,
            datetime64_val DateTime64(3),

            -- 布尔类型
            bool_val Bool,

            -- 数组类型
            int_array Array(Int32),
            text_array Array(String),

            -- UUID类型
            uuid_val UUID,

            -- 枚举类型
            enum_val Enum8('small' = 1, 'medium' = 2, 'large' = 3)
        )
        ENGINE = MergeTree()
        ORDER BY id
        """

    def _generate_comprehensive_test_data(
        self, count: int = 50
    ) -> list[dict[str, Any]]:
        """
        生成包含各种数据类型的测试数据。

        参数:
            count: 生成数据的数量

        返回:
            list[dict[str, Any]]: 测试数据列表
        """
        fake = Faker("zh_CN")
        data = []

        for i in range(count):
            data.append(
                {
                    # 数值类型
                    "id": i + 1,
                    "small_int": random.randint(-32768, 32767),
                    "big_int": random.randint(-2147483648, 2147483647),
                    "decimal_val": Decimal(
                        str(random.uniform(-999.9999, 999.9999))
                    ).quantize(Decimal("0.0001")),
                    "float_val": random.uniform(-1000.0, 1000.0),
                    "double_val": random.uniform(-10000.0, 10000.0),
                    # 字符串类型
                    "varchar_val": fake.text(max_nb_chars=200),
                    "fixed_string_val": fake.lexify(text="??????????"),  # 10个字符
                    # 日期时间类型
                    "date_val": fake.date_between(start_date="-1y", end_date="today"),
                    "datetime_val": fake.date_time_between(
                        start_date="-1y", end_date="now"
                    ),
                    "datetime64_val": datetime.now(UTC),
                    # 布尔类型
                    "bool_val": random.choice([True, False]),
                    # 数组类型
                    "int_array": [
                        random.randint(1, 100) for _ in range(random.randint(1, 5))
                    ],
                    "text_array": fake.words(nb=random.randint(2, 6)),
                    # UUID类型
                    "uuid_val": fake.uuid4(),
                    # 枚举类型
                    "enum_val": random.choice(["small", "medium", "large"]),
                }
            )

        return data

    def _create_primary_key_conflict_table_schema(self, table_name: str) -> str:
        """创建用于主键冲突测试的表SQL架构"""
        return f"""
        CREATE TABLE {table_name} (
            id UInt32,
            name String,
            value UInt32,
            updated_at DateTime
        )
        ENGINE = ReplacingMergeTree(updated_at)
        ORDER BY id
        """

    def _generate_conflict_test_data(
        self, base_count: int = 20, conflict_count: int = 5
    ) -> tuple[list[dict[str, Any]], list[dict[str, Any]]]:
        """
        生成主键冲突测试数据。

        参数:
            base_count: 基础数据数量
            conflict_count: 冲突数据数量

        返回:
            tuple: (基础数据, 冲突数据)
        """
        fake = Faker("zh_CN")

        # 生成基础数据
        base_data = []
        for i in range(1, base_count + 1):
            base_data.append(
                {
                    "id": i,
                    "name": fake.name(),
                    "value": random.randint(1, 1000),
                    "updated_at": fake.date_time_between(
                        start_date="-1y", end_date="-1m"
                    ),
                }
            )

        # 生成冲突数据（使用相同的ID但不同的值）
        conflict_data = []
        conflict_ids = random.sample(
            range(1, base_count + 1), min(conflict_count, base_count)
        )
        for id_val in conflict_ids:
            conflict_data.append(
                {
                    "id": id_val,
                    "name": fake.name(),
                    "value": random.randint(1001, 2000),  # 不同的值范围
                    "updated_at": fake.date_time_between(
                        start_date="-1w", end_date="now"
                    ),
                }
            )

        return base_data, conflict_data

    # ==================== 测试用例 ====================

    @pytest.mark.clickhouse
    @pytest.mark.minio
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_basic_export_import_roundtrip(
        self, clickhouse_config: ClickHouseConfig
    ):
        """
        测试基本的导出导入往返流程。

        验证功能：
        1. 创建测试表并插入数据
        2. 导出数据到S3
        3. 导入数据到新表
        4. 验证数据完整性和一致性
        """
        source_table = self._generate_unique_table_name("test_basic_source")
        target_table = self._generate_unique_table_name("test_basic_target")
        s3_key = self._create_s3_key("basic_roundtrip")

        try:
            # 1. 创建源表并插入测试数据
            await self._create_test_table(
                source_table, self._create_basic_test_table_schema(source_table)
            )
            test_data = self._generate_basic_test_data(100)
            await self._insert_test_data(source_table, test_data)

            # 验证源表数据
            source_count = await self._count_table_rows(source_table)
            assert source_count == 100, f"Expected 100 rows, got {source_count}"

            # 2. 导出数据到S3
            extractor = ClickHouseExtractor(clickhouse_config)
            query_config = QueryConfig(table_name=source_table)
            export_config = ExportConfig(
                source_db=clickhouse_config,
                query=query_config,
                s3_target=self.s3_config,
                cursor_fetch_size=20,  # 测试分批
                parquet_chunk_size=30,
            )

            # 收集导出的数据块
            exported_chunks = []
            async for chunk in extractor.extract_stream(query_config, export_config):
                assert isinstance(chunk, pd.DataFrame)
                assert not chunk.empty
                exported_chunks.append(chunk)

            # 验证导出数据
            assert len(exported_chunks) > 0
            total_exported = sum(len(chunk) for chunk in exported_chunks)
            assert total_exported == 100

            # 3. 创建目标表并导入数据
            await self._create_test_table(
                target_table, self._create_basic_test_table_schema(target_table)
            )

            loader = ClickHouseLoader(clickhouse_config)
            import_config = ImportConfig(
                table_name=target_table,
                s3_source=self.s3_config,
                target_db=clickhouse_config,
                conflict_strategy=ConflictStrategy.IGNORE,
            )

            # 导入所有数据块
            for chunk in exported_chunks:
                await loader.load_chunk(chunk, import_config)

            # 4. 验证导入的数据
            target_count = await self._count_table_rows(target_table)
            assert target_count == 100

            # 比较源表和目标表数据
            source_data = await self._fetch_table_data(source_table, "id")
            target_data = await self._fetch_table_data(target_table, "id")

            assert len(source_data) == len(target_data)
            for i, (src_row, tgt_row) in enumerate(
                zip(source_data, target_data, strict=False)
            ):
                assert src_row["id"] == tgt_row["id"], f"ID mismatch at row {i}"
                assert src_row["name"] == tgt_row["name"], f"Name mismatch at row {i}"
                assert src_row["age"] == tgt_row["age"], f"Age mismatch at row {i}"
                # 比较 Decimal 值
                assert float(src_row["salary"]) == pytest.approx(
                    float(tgt_row["salary"]), rel=1e-5
                ), f"Salary mismatch at row {i}"
                assert src_row["is_active"] == tgt_row["is_active"], (
                    f"Is_active mismatch at row {i}"
                )

            logger.info("Basic export/import roundtrip test passed successfully")

        finally:
            # 清理测试表
            await self.clickhouse_client.command(f"DROP TABLE IF EXISTS {source_table}")
            await self.clickhouse_client.command(f"DROP TABLE IF EXISTS {target_table}")

    @pytest.mark.clickhouse
    @pytest.mark.minio
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_comprehensive_data_types_compatibility(
        self, clickhouse_config: ClickHouseConfig
    ):
        """
        测试所有支持的数据类型兼容性。

        验证功能：
        1. 创建包含各种数据类型的测试表
        2. 导出和导入包含不同数据类型的数据
        3. 验证每种数据类型的准确性
        """
        source_table = self._generate_unique_table_name("test_types_source")
        target_table = self._generate_unique_table_name("test_types_target")
        s3_key = self._create_s3_key("data_types")

        try:
            # 1. 创建源表并插入测试数据
            await self._create_test_table(
                source_table,
                self._create_comprehensive_data_types_table_schema(source_table),
            )
            test_data = self._generate_comprehensive_test_data(50)
            await self._insert_test_data(source_table, test_data)

            # 2. 导出数据
            extractor = ClickHouseExtractor(clickhouse_config)
            query_config = QueryConfig(table_name=source_table)
            export_config = ExportConfig(
                source_db=clickhouse_config,
                query=query_config,
                s3_target=self.s3_config,
                cursor_fetch_size=10,
            )

            exported_chunks = []
            async for chunk in extractor.extract_stream(query_config, export_config):
                exported_chunks.append(chunk)

            # 3. 创建目标表并导入数据
            await self._create_test_table(
                target_table,
                self._create_comprehensive_data_types_table_schema(target_table),
            )

            loader = ClickHouseLoader(clickhouse_config)
            import_config = ImportConfig(
                table_name=target_table,
                s3_source=self.s3_config,
                target_db=clickhouse_config,
                conflict_strategy=ConflictStrategy.IGNORE,
            )

            for chunk in exported_chunks:
                await loader.load_chunk(chunk, import_config)

            # 4. 验证数据类型准确性
            source_data = await self._fetch_table_data(source_table, "id")
            target_data = await self._fetch_table_data(target_table, "id")

            assert len(source_data) == len(target_data)
            for i, (src_row, tgt_row) in enumerate(
                zip(source_data, target_data, strict=False)
            ):
                # 验证数值类型
                assert src_row["id"] == tgt_row["id"]
                assert src_row["small_int"] == tgt_row["small_int"]
                assert src_row["big_int"] == tgt_row["big_int"]
                assert float(src_row["decimal_val"]) == pytest.approx(
                    float(tgt_row["decimal_val"]), rel=1e-5
                )
                assert src_row["float_val"] == pytest.approx(
                    tgt_row["float_val"], rel=1e-5
                )
                assert src_row["double_val"] == pytest.approx(
                    tgt_row["double_val"], rel=1e-9
                )

                # 验证字符串类型
                assert src_row["varchar_val"] == tgt_row["varchar_val"]
                assert src_row["fixed_string_val"] == tgt_row["fixed_string_val"]

                # 验证布尔类型
                assert src_row["bool_val"] == tgt_row["bool_val"]

                # 验证数组类型
                assert src_row["int_array"] == tgt_row["int_array"]
                assert src_row["text_array"] == tgt_row["text_array"]

                # 验证UUID类型
                assert src_row["uuid_val"] == tgt_row["uuid_val"]

                # 验证枚举类型
                assert src_row["enum_val"] == tgt_row["enum_val"]

            logger.info("Comprehensive data types compatibility test passed")

        finally:
            # 清理测试表
            await self.clickhouse_client.command(f"DROP TABLE IF EXISTS {source_table}")
            await self.clickhouse_client.command(f"DROP TABLE IF EXISTS {target_table}")

    @pytest.mark.clickhouse
    @pytest.mark.minio
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_batch_export_large_dataset(
        self, clickhouse_config: ClickHouseConfig
    ):
        """
        测试大数据集的分批导出功能。

        验证功能：
        1. 创建包含大量数据的测试表
        2. 测试不同批次大小的导出性能
        3. 验证分批导出的正确性
        4. 确保所有数据都被正确导出
        """
        table_name = self._generate_unique_table_name("test_batch_export")
        s3_key = self._create_s3_key("batch_export")

        # 生成更大的数据集
        large_data_count = 1000

        try:
            # 1. 创建表并插入大量数据
            await self._create_test_table(
                table_name, self._create_basic_test_table_schema(table_name)
            )
            test_data = self._generate_basic_test_data(large_data_count)
            await self._insert_test_data(table_name, test_data)

            # 2. 测试不同的批次大小
            batch_sizes = [50, 100, 200]
            for batch_size in batch_sizes:
                extractor = ClickHouseExtractor(clickhouse_config)
                query_config = QueryConfig(table_name=table_name)
                export_config = ExportConfig(
                    source_db=clickhouse_config,
                    query=query_config,
                    s3_target=self.s3_config,
                    cursor_fetch_size=batch_size,
                    parquet_chunk_size=batch_size * 2,
                )

                # 收集导出的批次信息
                batch_count = 0
                total_rows = 0
                chunk_sizes = []

                async for chunk in extractor.extract_stream(
                    query_config, export_config
                ):
                    batch_count += 1
                    chunk_size = len(chunk)
                    total_rows += chunk_size
                    chunk_sizes.append(chunk_size)

                    # 验证批次大小
                    assert chunk_size <= batch_size * 2, (
                        f"Chunk size {chunk_size} exceeds expected maximum {batch_size * 2}"
                    )

                # 验证导出完整性
                assert total_rows == large_data_count, (
                    f"Expected {large_data_count} rows, got {total_rows}"
                )
                assert batch_count > 1, (
                    f"Expected multiple batches with batch_size={batch_size}"
                )

                logger.info(
                    f"Batch export with size={batch_size}: "
                    f"{batch_count} batches, chunk sizes: {chunk_sizes[:5]}..."
                )

            logger.info("Batch export test passed successfully")

        finally:
            # 清理测试表
            await self.clickhouse_client.command(f"DROP TABLE IF EXISTS {table_name}")

    @pytest.mark.clickhouse
    @pytest.mark.minio
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_primary_key_conflict_strategies(
        self, clickhouse_config: ClickHouseConfig
    ):
        """
        测试主键冲突处理策略。

        验证功能：
        1. 测试 IGNORE 策略（保留原有数据）
        2. 测试 REPLACE 策略（使用 ReplacingMergeTree）
        """
        source_table = self._generate_unique_table_name("test_conflict_source")
        target_table = self._generate_unique_table_name("test_conflict_target")
        s3_key = self._create_s3_key("conflict_handling")

        try:
            # 1. 创建源表和目标表（使用 ReplacingMergeTree）
            await self._create_test_table(
                source_table,
                self._create_primary_key_conflict_table_schema(source_table),
            )
            await self._create_test_table(
                target_table,
                self._create_primary_key_conflict_table_schema(target_table),
            )

            # 2. 生成测试数据
            base_data, conflict_data = self._generate_conflict_test_data(20, 5)

            # 3. 插入基础数据到两个表
            await self._insert_test_data(source_table, base_data)
            await self._insert_test_data(target_table, base_data)

            # 4. 插入冲突数据到源表
            await self._insert_test_data(source_table, conflict_data)

            # 5. 测试 IGNORE 策略
            # 导出源表数据
            extractor = ClickHouseExtractor(clickhouse_config)
            query_config = QueryConfig(
                table_name=source_table,
                # 只导出冲突的数据
                columns=["id", "name", "value", "updated_at"],
                sql_condition=f"id IN ({','.join(str(d['id']) for d in conflict_data)})",
            )
            export_config = ExportConfig(
                source_db=clickhouse_config,
                query=query_config,
                s3_target=self.s3_config,
            )

            conflict_chunks = []
            async for chunk in extractor.extract_stream(query_config, export_config):
                conflict_chunks.append(chunk)

            # 使用 IGNORE 策略导入（数据应该不变）
            loader = ClickHouseLoader(clickhouse_config)
            import_config = ImportConfig(
                table_name=target_table,
                s3_source=self.s3_config,
                target_db=clickhouse_config,
                conflict_strategy=ConflictStrategy.IGNORE,
            )

            for chunk in conflict_chunks:
                await loader.load_chunk(chunk, import_config)

            # 强制合并以确保 ReplacingMergeTree 生效
            await self.clickhouse_client.command(f"OPTIMIZE TABLE {target_table} FINAL")

            # 验证数据（应该保持原有的base_data）
            target_data = await self._fetch_table_data(target_table, "id")

            # 对于 ReplacingMergeTree，最终会保留最新的记录
            # 所以我们验证记录数量和更新时间
            assert len(target_data) == 20  # 应该只有20条唯一记录

            # 6. 测试 REPLACE 策略
            # 重置目标表
            await self.clickhouse_client.command(f"TRUNCATE TABLE {target_table}")
            await self._insert_test_data(target_table, base_data)

            # 使用 REPLACE 策略导入所有源表数据
            query_config_all = QueryConfig(table_name=source_table)
            export_config_all = ExportConfig(
                source_db=clickhouse_config,
                query=query_config_all,
                s3_target=self.s3_config,
            )

            all_chunks = []
            async for chunk in extractor.extract_stream(
                query_config_all, export_config_all
            ):
                all_chunks.append(chunk)

            import_config_replace = ImportConfig(
                table_name=target_table,
                s3_source=self.s3_config,
                target_db=clickhouse_config,
                conflict_strategy=ConflictStrategy.REPLACE,
            )

            for chunk in all_chunks:
                await loader.load_chunk(chunk, import_config_replace)

            # 强制合并
            await self.clickhouse_client.command(f"OPTIMIZE TABLE {target_table} FINAL")

            # 验证数据（应该包含更新后的数据）
            final_data = await self._fetch_table_data(target_table, "id")
            assert len(final_data) == 20  # ReplacingMergeTree会保留最新版本

            # 验证冲突ID的数据已被更新
            for conflict_item in conflict_data:
                matching_rows = [
                    row for row in final_data if row["id"] == conflict_item["id"]
                ]
                assert len(matching_rows) == 1
                # ReplacingMergeTree会保留updated_at最新的记录
                assert matching_rows[0]["value"] >= 1001  # 冲突数据的value范围

            logger.info("Primary key conflict handling test passed")

        finally:
            # 清理测试表
            await self.clickhouse_client.command(f"DROP TABLE IF EXISTS {source_table}")
            await self.clickhouse_client.command(f"DROP TABLE IF EXISTS {target_table}")

    @pytest.mark.clickhouse
    @pytest.mark.minio
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_metadata_provider(self, clickhouse_config: ClickHouseConfig):
        """
        测试ClickHouse元数据提供者功能。

        验证功能：
        1. 获取表结构信息
        2. 获取列信息
        3. 获取索引信息
        4. 验证表存在性检查
        """
        table_name = self._generate_unique_table_name("test_metadata")

        try:
            # 创建测试表
            table_schema = f"""
            CREATE TABLE {table_name} (
                id UInt32 COMMENT '主键ID',
                name String COMMENT '用户名称',
                email String COMMENT '电子邮箱',
                age UInt8 COMMENT '年龄',
                score Decimal(5,2) DEFAULT 0.0 COMMENT '分数',
                is_active Bool DEFAULT true COMMENT '是否激活',
                created_at DateTime DEFAULT now() COMMENT '创建时间',
                updated_at DateTime DEFAULT now() COMMENT '更新时间'
            )
            ENGINE = MergeTree()
            ORDER BY (id, email)
            PRIMARY KEY id
            COMMENT '用户信息表'
            """
            await self.clickhouse_client.command(table_schema)

            # 使用元数据提供者
            metadata_provider = ClickHouseMetadataProvider(clickhouse_config)

            # 测试表存在性检查
            exists = await metadata_provider.table_exists(table_name)
            assert exists is True

            # 获取表元数据
            metadata = await metadata_provider.get_table_metadata(table_name)

            assert metadata is not None
            assert metadata.table_name == table_name
            assert len(metadata.columns) == 8  # 8个列

            # 验证列信息
            column_names = [col.name for col in metadata.columns]
            assert "id" in column_names
            assert "name" in column_names
            assert "email" in column_names
            assert "age" in column_names

            # 验证主键
            primary_keys = [col.name for col in metadata.columns if col.is_primary_key]
            assert "id" in primary_keys

            # 验证索引信息
            assert len(metadata.indexes) > 0
            primary_index = next(
                (idx for idx in metadata.indexes if idx.is_primary), None
            )
            assert primary_index is not None
            assert "id" in primary_index.columns

            # 验证表注释
            assert metadata.comment == "用户信息表"

            # 测试不存在的表
            non_exists = await metadata_provider.table_exists("non_existent_table")
            assert non_exists is False

            logger.info("Metadata provider test passed")

        finally:
            # 清理测试表
            await self.clickhouse_client.command(f"DROP TABLE IF EXISTS {table_name}")

    def test_suite_summary(self):
        """
        测试套件总结。

        输出测试覆盖的功能点和注意事项。
        """
        summary = """
        ClickHouse 导入导出测试套件已覆盖以下功能：
        
        1. 基础功能测试
           - 数据提取和加载的完整流程
           - 流式处理和批量处理
           - S3存储集成
        
        2. 数据类型兼容性
           - 数值类型：Int8/16/32/64, Float32/64, Decimal
           - 字符串类型：String, FixedString
           - 日期时间类型：Date, DateTime, DateTime64
           - 布尔类型：Bool
           - 复杂类型：Array, UUID, Enum
        
        3. 批量导出性能
           - 大数据集处理
           - 自定义批次大小
           - 内存效率优化
        
        4. 冲突处理策略
           - IGNORE策略：保留现有数据
           - REPLACE策略：使用ReplacingMergeTree特性
        
        5. 元数据管理
           - 表结构查询
           - 列信息获取
           - 索引信息提取
           - 表存在性检查
        
        注意事项：
        - ClickHouse的表必须预先创建，需指定合适的引擎类型
        - ReplacingMergeTree需要OPTIMIZE FINAL才能确保数据合并
        - 批量导入性能取决于表引擎和数据类型
        - 时区处理依赖于ClickHouse服务器配置
        """
        logger.info(summary)
        print(summary)
