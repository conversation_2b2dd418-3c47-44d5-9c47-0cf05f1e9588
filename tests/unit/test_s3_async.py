#!/usr/bin/env python3

"""
AsyncS3Client 单元测试模块。

本模块提供了 AsyncS3Client 类的完整单元测试，包括：
1. 基础文件上传下载测试
2. 对象列表和删除测试
3. 批量上传测试
4. 生命周期策略管理测试
5. 错误处理测试
6. 上下文管理器测试

使用 moto 库模拟 S3 服务，确保测试的独立性和可重复性。
"""

import asyncio
import tempfile
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
import yaml
from moto import mock_aws
from pydantic import SecretStr

from datax.models import S3Config
from datax.utils.s3 import AsyncS3Client


@pytest.fixture
def s3_config():
    """创建测试用的S3配置"""
    return S3Config(
        access_key="test-access-key",
        secret_key=SecretStr("test-secret-key"),
        bucket="test-bucket",
        key="test/",
        region="us-east-1",
        endpoint_url="http://localhost:9000",
        max_concurrency=5,
        max_bandwidth_mbps=100,
    )


@pytest.fixture
def temp_file():
    """创建临时测试文件"""
    with tempfile.NamedTemporaryFile(mode="w", delete=False, suffix=".txt") as f:
        f.write("Hello, S3 Test!")
        temp_path = f.name

    yield temp_path

    # 清理
    Path(temp_path).unlink(missing_ok=True)


@pytest.fixture
def lifecycle_config_file():
    """创建临时生命周期配置文件"""
    config_data = {
        "rules": [
            {"id": "test-rule-1", "prefix": "logs/", "expiration_days": 30},
            {"id": "test-rule-2", "prefix": "temp/", "expiration_days": 7},
        ]
    }

    with tempfile.NamedTemporaryFile(mode="w", delete=False, suffix=".yml") as f:
        yaml.dump(config_data, f)
        config_path = f.name

    yield config_path

    # 清理
    Path(config_path).unlink(missing_ok=True)


class TestAsyncS3Client:
    """AsyncS3Client 单元测试类"""

    @pytest.mark.asyncio
    async def test_context_manager_initialization(self, s3_config):
        """测试上下文管理器的正确初始化和清理"""
        async with AsyncS3Client(s3_config) as client:
            assert client.client is not None
            assert client.s3_config == s3_config

    @pytest.mark.asyncio
    async def test_client_not_initialized_error(self, s3_config):
        """测试客户端未初始化时的错误处理"""
        client = AsyncS3Client(s3_config)

        with pytest.raises(RuntimeError, match="Client not initialized"):
            await client.upload_file("test.txt", "test-key")

        with pytest.raises(RuntimeError, match="Client not initialized"):
            await client.download_file("test-key", "test.txt")

        with pytest.raises(RuntimeError, match="Client not initialized"):
            await client.delete_object("test-key")

        with pytest.raises(RuntimeError, match="Client not initialized"):
            async for _ in client.list_objects():
                pass

    @pytest.mark.asyncio
    async def test_upload_file_success(self, s3_config, temp_file):
        """测试文件上传成功"""
        with patch("datax.utils.s3.aioboto3.Session") as mock_session:
            mock_client = AsyncMock()
            mock_session.return_value.client.return_value.__aenter__.return_value = (
                mock_client
            )

            async with AsyncS3Client(s3_config) as client:
                client.client = mock_client
                await client.upload_file(temp_file, "test-key")

                mock_client.upload_file.assert_called_once()
                call_args = mock_client.upload_file.call_args
                assert call_args[0][0] == temp_file
                assert call_args[0][1] == s3_config.bucket
                assert call_args[0][2] == "test-key"

    @pytest.mark.asyncio
    async def test_download_file_success(self, s3_config, temp_file):
        """测试文件下载成功"""
        with patch("datax.utils.s3.aioboto3.Session") as mock_session:
            mock_client = AsyncMock()
            mock_session.return_value.client.return_value.__aenter__.return_value = (
                mock_client
            )

            async with AsyncS3Client(s3_config) as client:
                client.client = mock_client
                await client.download_file("test-key", temp_file)

                mock_client.download_file.assert_called_once()
                call_args = mock_client.download_file.call_args
                assert call_args[0][0] == s3_config.bucket
                assert call_args[0][1] == "test-key"
                assert call_args[0][2] == temp_file

    @pytest.mark.asyncio
    async def test_delete_object_success(self, s3_config):
        """测试对象删除成功"""
        with patch("datax.utils.s3.aioboto3.Session") as mock_session:
            mock_client = AsyncMock()
            mock_session.return_value.client.return_value.__aenter__.return_value = (
                mock_client
            )

            async with AsyncS3Client(s3_config) as client:
                client.client = mock_client
                await client.delete_object("test-key")

                mock_client.delete_object.assert_called_once_with(
                    Bucket=s3_config.bucket, Key="test-key"
                )

    @pytest.mark.asyncio
    async def test_list_objects_success(self, s3_config):
        """测试对象列表成功"""
        async with AsyncS3Client(s3_config) as client:
            # 直接mock list_objects方法
            async def mock_list_objects(prefix=""):
                test_objects = ["file1.txt", "file2.txt", "file3.txt"]
                for obj in test_objects:
                    yield obj

            client.list_objects = mock_list_objects

            objects = []
            async for obj_key in client.list_objects("test-prefix"):
                objects.append(obj_key)

            assert objects == ["file1.txt", "file2.txt", "file3.txt"]

    @pytest.mark.asyncio
    async def test_upload_multiple_files_success(self, s3_config, temp_file):
        """测试批量文件上传成功"""
        with patch("datax.utils.s3.aioboto3.Session") as mock_session:
            mock_client = AsyncMock()
            mock_session.return_value.client.return_value.__aenter__.return_value = (
                mock_client
            )

            # 创建多个临时文件
            file_mappings = [
                (temp_file, "key1"),
                (temp_file, "key2"),
                (temp_file, "key3"),
            ]

            async with AsyncS3Client(s3_config) as client:
                client.client = mock_client
                await client.upload_multiple_files(
                    file_mappings, max_concurrent_uploads=2
                )

                # 验证上传方法被调用了正确的次数
                assert mock_client.upload_file.call_count == 3

    @pytest.mark.asyncio
    async def test_apply_lifecycle_policy_success(
        self, s3_config, lifecycle_config_file
    ):
        """测试生命周期策略应用成功"""
        with patch("datax.utils.s3.aioboto3.Session") as mock_session:
            mock_client = AsyncMock()
            mock_session.return_value.client.return_value.__aenter__.return_value = (
                mock_client
            )

            async with AsyncS3Client(s3_config) as client:
                client.client = mock_client
                await client.apply_lifecycle_policy(lifecycle_config_file)

                mock_client.put_bucket_lifecycle_configuration.assert_called_once()
                call_args = mock_client.put_bucket_lifecycle_configuration.call_args
                assert call_args[1]["Bucket"] == s3_config.bucket

                # 验证生命周期配置结构
                lifecycle_config = call_args[1]["LifecycleConfiguration"]
                assert "Rules" in lifecycle_config
                assert len(lifecycle_config["Rules"]) == 2

    @pytest.mark.asyncio
    async def test_apply_lifecycle_policy_file_not_found(self, s3_config):
        """测试生命周期策略文件不存在的错误处理"""
        async with AsyncS3Client(s3_config) as client:
            with pytest.raises(FileNotFoundError):
                await client.apply_lifecycle_policy("nonexistent-file.yml")

    @pytest.mark.asyncio
    async def test_apply_lifecycle_policy_empty_rules(self, s3_config):
        """测试空规则的生命周期策略"""
        # 创建空规则配置文件
        empty_config = {"rules": []}
        with tempfile.NamedTemporaryFile(mode="w", delete=False, suffix=".yml") as f:
            yaml.dump(empty_config, f)
            config_path = f.name

        try:
            with patch("datax.utils.s3.aioboto3.Session") as mock_session:
                mock_client = AsyncMock()
                mock_session.return_value.client.return_value.__aenter__.return_value = mock_client

                async with AsyncS3Client(s3_config) as client:
                    client.client = mock_client
                    await client.apply_lifecycle_policy(config_path)

                    # 空规则时不应该调用 put_bucket_lifecycle_configuration
                    mock_client.put_bucket_lifecycle_configuration.assert_not_called()
        finally:
            Path(config_path).unlink(missing_ok=True)
